import React, { useState } from 'react';
import { Plus, Edit, Eye, Trash2 } from 'lucide-react';
import {
  ClientModal,
  CommercialModal,
  ProductModal,
  OrderModal,
  MerchandizerModal,
  CatalogueModal
} from '../components/modals';
import { useApp } from '../hooks/useApp';

const ModalUsageExample: React.FC = () => {
  const { 
    clients, 
    commercials, 
    products, 
    orders, 
    merchandizers, 
    catalogues,
    deleteClient,
    deleteCommercial,
    deleteProduct,
    deleteOrder,
    deleteMerchandizer,
    deleteCatalogue
  } = useApp();

  // Modal states
  const [clientModal, setClientModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    client?: any;
  }>({ isOpen: false, mode: 'create' });

  const [commercialModal, setCommercialModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    commercial?: any;
  }>({ isOpen: false, mode: 'create' });

  const [productModal, setProductModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    product?: any;
  }>({ isOpen: false, mode: 'create' });

  const [orderModal, setOrderModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    order?: any;
  }>({ isOpen: false, mode: 'create' });

  const [merchandizerModal, setMerchandizerModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    merchandizer?: any;
  }>({ isOpen: false, mode: 'create' });

  const [catalogueModal, setCatalogueModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    catalogue?: any;
  }>({ isOpen: false, mode: 'create' });

  // Helper functions to open modals
  const openClientModal = (mode: 'create' | 'edit' | 'view', client?: any) => {
    setClientModal({ isOpen: true, mode, client });
  };

  const openCommercialModal = (mode: 'create' | 'edit' | 'view', commercial?: any) => {
    setCommercialModal({ isOpen: true, mode, commercial });
  };

  const openProductModal = (mode: 'create' | 'edit' | 'view', product?: any) => {
    setProductModal({ isOpen: true, mode, product });
  };

  const openOrderModal = (mode: 'create' | 'edit' | 'view', order?: any) => {
    setOrderModal({ isOpen: true, mode, order });
  };

  const openMerchandizerModal = (mode: 'create' | 'edit' | 'view', merchandizer?: any) => {
    setMerchandizerModal({ isOpen: true, mode, merchandizer });
  };

  const openCatalogueModal = (mode: 'create' | 'edit' | 'view', catalogue?: any) => {
    setCatalogueModal({ isOpen: true, mode, catalogue });
  };

  // Helper function to close modals
  const closeModals = () => {
    setClientModal({ isOpen: false, mode: 'create' });
    setCommercialModal({ isOpen: false, mode: 'create' });
    setProductModal({ isOpen: false, mode: 'create' });
    setOrderModal({ isOpen: false, mode: 'create' });
    setMerchandizerModal({ isOpen: false, mode: 'create' });
    setCatalogueModal({ isOpen: false, mode: 'create' });
  };

  // Action buttons component
  const ActionButtons: React.FC<{ 
    onView: () => void; 
    onEdit: () => void; 
    onDelete: () => void; 
  }> = ({ onView, onEdit, onDelete }) => (
    <div className="flex space-x-2">
      <button
        onClick={onView}
        className="p-2 text-blue-600 hover:bg-blue-100 rounded-md transition-colors"
        title="Voir"
      >
        <Eye className="h-4 w-4" />
      </button>
      <button
        onClick={onEdit}
        className="p-2 text-green-600 hover:bg-green-100 rounded-md transition-colors"
        title="Modifier"
      >
        <Edit className="h-4 w-4" />
      </button>
      <button
        onClick={onDelete}
        className="p-2 text-red-600 hover:bg-red-100 rounded-md transition-colors"
        title="Supprimer"
      >
        <Trash2 className="h-4 w-4" />
      </button>
    </div>
  );

  return (
    <div className="p-6 space-y-8">
      <h1 className="text-3xl font-bold text-gray-900">Exemple d'utilisation des Modales CRUD</h1>
      
      {/* Clients Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Clients</h2>
          <button
            onClick={() => openClientModal('create')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Nouveau Client</span>
          </button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entreprise</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {clients.map((client) => (
                <tr key={client.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{client.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{client.email}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{client.company}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      client.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {client.status === 'active' ? 'Actif' : 'Inactif'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <ActionButtons
                      onView={() => openClientModal('view', client)}
                      onEdit={() => openClientModal('edit', client)}
                      onDelete={() => client.id && deleteClient(client.id)}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Similar sections for other entities would go here... */}
      
      {/* All Modals */}
      <ClientModal
        isOpen={clientModal.isOpen}
        onClose={closeModals}
        client={clientModal.client}
        mode={clientModal.mode}
      />
      
      <CommercialModal
        isOpen={commercialModal.isOpen}
        onClose={closeModals}
        commercial={commercialModal.commercial}
        mode={commercialModal.mode}
      />
      
      <ProductModal
        isOpen={productModal.isOpen}
        onClose={closeModals}
        product={productModal.product}
        mode={productModal.mode}
      />
      
      <OrderModal
        isOpen={orderModal.isOpen}
        onClose={closeModals}
        order={orderModal.order}
        mode={orderModal.mode}
      />
      
      <MerchandizerModal
        isOpen={merchandizerModal.isOpen}
        onClose={closeModals}
        merchandizer={merchandizerModal.merchandizer}
        mode={merchandizerModal.mode}
      />
      
      <CatalogueModal
        isOpen={catalogueModal.isOpen}
        onClose={closeModals}
        catalogue={catalogueModal.catalogue}
        mode={catalogueModal.mode}
      />
    </div>
  );
};

export default ModalUsageExample;
