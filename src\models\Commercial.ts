import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  EntityStatus, 
  ContactInfo, 
  Address, 
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// COMMERCIAL INTERFACES
// ============================================================================

/**
 * Commercial role enum
 */
export enum CommercialRole {
  JUNIOR = 'junior',
  SENIOR = 'senior',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  REGIONAL_MANAGER = 'regional_manager'
}

/**
 * Employment type enum
 */
export enum EmploymentType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  FREELANCE = 'freelance',
  INTERN = 'intern'
}

/**
 * Territory type enum
 */
export enum TerritoryType {
  GEOGRAPHIC = 'geographic',
  INDUSTRY = 'industry',
  ACCOUNT_BASED = 'account_based',
  PRODUCT_LINE = 'product_line'
}

/**
 * Account status enum for mobile app access
 */
export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_ACTIVATION = 'pending_activation',
  LOCKED = 'locked'
}

/**
 * Mobile app permissions enum for commercials
 */
export enum CommercialMobilePermission {
  VIEW_CLIENTS = 'view_clients',
  EDIT_CLIENTS = 'edit_clients',
  CREATE_ORDERS = 'create_orders',
  EDIT_ORDERS = 'edit_orders',
  VIEW_PRODUCTS = 'view_products',
  VIEW_REPORTS = 'view_reports',
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_TERRITORY = 'manage_territory',
  SYNC_OFFLINE = 'sync_offline',
  ACCESS_PRICING = 'access_pricing',
  APPROVE_DISCOUNTS = 'approve_discounts'
}

/**
 * Commercial mobile app account interface
 */
export interface CommercialAccount {
  // Authentication credentials
  username: string;
  email: string;
  passwordHash?: string; // Never store plain password
  temporaryPassword?: string; // For initial setup, expires after first login

  // Account status and security
  accountStatus: AccountStatus;
  isFirstLogin: boolean;
  mustChangePassword: boolean;
  lastLoginDate?: Timestamp;
  lastPasswordChange?: Timestamp;
  failedLoginAttempts: number;
  lockedUntil?: Timestamp;

  // Mobile app permissions
  permissions: CommercialMobilePermission[];
  canAccessOffline: boolean;
  maxOfflineDays: number;

  // Device management
  registeredDevices: {
    deviceId: string;
    deviceName: string;
    platform: 'ios' | 'android';
    appVersion: string;
    registeredDate: Timestamp;
    lastActiveDate: Timestamp;
    isActive: boolean;
    pushToken?: string;
  }[];

  // Security settings
  requireBiometric: boolean;
  sessionTimeout: number; // minutes
  allowMultipleDevices: boolean;

  // Sales specific settings
  canViewAllClients: boolean;
  canEditPricing: boolean;
  maxDiscountPercentage: number;
  requireApprovalAbove: number; // order amount

  // Account creation info
  createdBy: string; // Admin who created the account
  createdDate: Timestamp;
  activatedDate?: Timestamp;

  // Password reset
  resetToken?: string;
  resetTokenExpiry?: Timestamp;
  resetRequestedBy?: string;
}

/**
 * Commercial territory interface
 */
export interface CommercialTerritory {
  id: string;
  name: string;
  type: TerritoryType;
  description?: string;
  regions: string[];
  cities: string[];
  postalCodes: string[];
  industries?: string[];
  clientIds: string[];
  targetRevenue: number;
  actualRevenue: number;
}

/**
 * Commercial performance metrics
 */
export interface CommercialPerformance {
  // Sales Metrics
  totalSales: number;
  salesTarget: number;
  salesAchievementRate: number; // percentage
  
  // Client Metrics
  totalClients: number;
  newClientsAcquired: number;
  clientRetentionRate: number; // percentage
  
  // Activity Metrics
  totalCalls: number;
  totalMeetings: number;
  totalEmails: number;
  totalVisits: number;
  
  // Conversion Metrics
  leadsGenerated: number;
  leadsConverted: number;
  conversionRate: number; // percentage
  averageDealSize: number;
  
  // Time Metrics
  averageResponseTime: number; // in hours
  averageSalesCycle: number; // in days
  
  // Period
  periodStart: Timestamp;
  periodEnd: Timestamp;
  lastUpdated: Timestamp;
}

/**
 * Commercial goals and targets
 */
export interface CommercialTargets {
  // Annual Targets
  annualSalesTarget: number;
  annualClientTarget: number;
  annualRevenueTarget: number;
  
  // Quarterly Targets
  quarterlySalesTarget: number;
  quarterlyClientTarget: number;
  quarterlyRevenueTarget: number;
  
  // Monthly Targets
  monthlySalesTarget: number;
  monthlyClientTarget: number;
  monthlyRevenueTarget: number;
  
  // Activity Targets
  weeklyCallsTarget: number;
  weeklyMeetingsTarget: number;
  weeklyVisitsTarget: number;
  
  // Year
  targetYear: number;
}

/**
 * Commercial compensation structure
 */
export interface CommercialCompensation {
  baseSalary: number;
  commissionRate: number; // percentage
  bonusStructure: {
    targetBonus: number;
    achievementThresholds: {
      threshold: number; // percentage of target
      bonusMultiplier: number;
    }[];
  };
  benefits: string[];
  lastReviewDate?: Timestamp;
  nextReviewDate?: Timestamp;
}

/**
 * Commercial skills and certifications
 */
export interface CommercialSkills {
  languages: {
    language: string;
    proficiency: 'basic' | 'intermediate' | 'advanced' | 'native';
  }[];
  certifications: {
    name: string;
    issuer: string;
    dateObtained: Timestamp;
    expiryDate?: Timestamp;
    credentialId?: string;
  }[];
  skills: {
    skill: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    yearsOfExperience: number;
  }[];
}

/**
 * Main Commercial interface
 */
export interface Commercial extends BaseEntity {
  // Personal Information
  firstName: string;
  lastName: string;
  name: string; // Full name for display
  email: string;
  phone: string;
  mobile?: string;
  
  // Professional Information
  employeeId: string;
  role: CommercialRole;
  employmentType: EmploymentType;
  department: string;
  managerId?: string;
  managerName?: string;

  // Mobile App Account
  account: CommercialAccount;
  
  // Territory and Assignment
  territory: string;
  territoryDetails?: CommercialTerritory;
  assignedClients: string[];
  
  // Performance and Targets
  performance: CommercialPerformance;
  targets: CommercialTargets;
  
  // Compensation
  compensation: CommercialCompensation;
  
  // Skills and Development
  skills: CommercialSkills;
  
  // Employment Details
  hireDate: Timestamp;
  startDate: Timestamp;
  endDate?: Timestamp;
  
  // Contact and Address
  address?: Address;
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  
  // Status and Metadata
  status: EntityStatus;
  availability: 'available' | 'busy' | 'on_leave' | 'unavailable';
  workingHours: {
    monday: { start: string; end: string; };
    tuesday: { start: string; end: string; };
    wednesday: { start: string; end: string; };
    thursday: { start: string; end: string; };
    friday: { start: string; end: string; };
    saturday?: { start: string; end: string; };
    sunday?: { start: string; end: string; };
  };
  
  // Metadata
  notes?: string;
  tags: string[];
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// COMMERCIAL FORM INTERFACES
// ============================================================================

/**
 * Commercial form data interface (for forms)
 */
export interface CommercialFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  mobile?: string;
  employeeId: string;
  role: CommercialRole;
  employmentType: EmploymentType;
  department: string;
  territory: string;
  managerId?: string;
  hireDate: string; // ISO date string for forms
  startDate: string; // ISO date string for forms
  baseSalary: number;
  commissionRate: number;
  status: EntityStatus;
  availability: 'available' | 'busy' | 'on_leave' | 'unavailable';
  notes?: string;
  tags: string[];

  // Targets
  annualSalesTarget: number;
  annualClientTarget: number;
  annualRevenueTarget: number;
  weeklyCallsTarget: number;
  weeklyMeetingsTarget: number;

  // Mobile App Account Creation
  username: string;
  temporaryPassword: string;
  permissions: CommercialMobilePermission[];
  canAccessOffline: boolean;
  maxOfflineDays: number;
  requireBiometric: boolean;
  sessionTimeout: number;
  allowMultipleDevices: boolean;
  canViewAllClients: boolean;
  canEditPricing: boolean;
  maxDiscountPercentage: number;
  requireApprovalAbove: number;
}

/**
 * Commercial search/filter interface
 */
export interface CommercialSearchFilters {
  name?: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  role?: CommercialRole;
  employmentType?: EmploymentType;
  department?: string;
  territory?: string;
  managerId?: string;
  status?: EntityStatus;
  availability?: 'available' | 'busy' | 'on_leave' | 'unavailable';
  tags?: string[];
  hiredAfter?: Timestamp;
  hiredBefore?: Timestamp;
  minSalesTarget?: number;
  maxSalesTarget?: number;
  performanceRating?: 'low' | 'medium' | 'high';
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateCommercialData = CreateEntity<Commercial>;
export type UpdateCommercialData = UpdateEntity<Commercial>;
export type CommercialWithId = Commercial & Required<Pick<Commercial, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate commercial form data
 */
export const validateCommercialData = (data: Partial<CommercialFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.firstName || data.firstName.trim().length === 0) {
    errors.push({
      field: 'firstName',
      message: 'Le prénom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.lastName || data.lastName.trim().length === 0) {
    errors.push({
      field: 'lastName',
      message: 'Le nom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push({
      field: 'email',
      message: 'L\'email est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Format d\'email invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.phone || data.phone.trim().length === 0) {
    errors.push({
      field: 'phone',
      message: 'Le téléphone est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.phone)) {
    errors.push({
      field: 'phone',
      message: 'Format de téléphone invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.employeeId || data.employeeId.trim().length === 0) {
    errors.push({
      field: 'employeeId',
      message: 'L\'ID employé est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.territory || data.territory.trim().length === 0) {
    errors.push({
      field: 'territory',
      message: 'Le territoire est requis',
      code: 'REQUIRED'
    });
  }

  // Business logic validations
  if (data.baseSalary && data.baseSalary < 0) {
    errors.push({
      field: 'baseSalary',
      message: 'Le salaire de base ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.commissionRate && (data.commissionRate < 0 || data.commissionRate > 100)) {
    errors.push({
      field: 'commissionRate',
      message: 'Le taux de commission doit être entre 0 et 100%',
      code: 'INVALID_VALUE'
    });
  }

  if (data.annualSalesTarget && data.annualSalesTarget < 0) {
    errors.push({
      field: 'annualSalesTarget',
      message: 'L\'objectif de ventes annuel ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  // Account validation
  if (!data.username || data.username.trim().length === 0) {
    errors.push({
      field: 'username',
      message: 'Le nom d\'utilisateur est requis',
      code: 'REQUIRED'
    });
  } else if (data.username.length < 3) {
    errors.push({
      field: 'username',
      message: 'Le nom d\'utilisateur doit contenir au moins 3 caractères',
      code: 'INVALID_LENGTH'
    });
  }

  if (!data.temporaryPassword || data.temporaryPassword.trim().length === 0) {
    errors.push({
      field: 'temporaryPassword',
      message: 'Le mot de passe temporaire est requis',
      code: 'REQUIRED'
    });
  } else if (data.temporaryPassword.length < 6) {
    errors.push({
      field: 'temporaryPassword',
      message: 'Le mot de passe doit contenir au moins 6 caractères',
      code: 'INVALID_LENGTH'
    });
  }

  if (!data.permissions || data.permissions.length === 0) {
    errors.push({
      field: 'permissions',
      message: 'Au moins une permission est requise',
      code: 'REQUIRED'
    });
  }

  if (data.maxDiscountPercentage !== undefined && (data.maxDiscountPercentage < 0 || data.maxDiscountPercentage > 100)) {
    errors.push({
      field: 'maxDiscountPercentage',
      message: 'Le pourcentage de remise maximum doit être entre 0 et 100%',
      code: 'INVALID_VALUE'
    });
  }

  if (data.requireApprovalAbove !== undefined && data.requireApprovalAbove < 0) {
    errors.push({
      field: 'requireApprovalAbove',
      message: 'Le montant d\'approbation ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize commercial data before saving
 */
export const sanitizeCommercialData = (data: CommercialFormData): CommercialFormData => {
  return {
    ...data,
    firstName: sanitizeString(data.firstName),
    lastName: sanitizeString(data.lastName),
    email: data.email.toLowerCase().trim(),
    phone: data.phone.replace(/[\s\-\(\)]/g, ''),
    mobile: data.mobile ? data.mobile.replace(/[\s\-\(\)]/g, '') : undefined,
    employeeId: sanitizeString(data.employeeId),
    department: sanitizeString(data.department),
    territory: sanitizeString(data.territory),
    notes: data.notes ? sanitizeString(data.notes) : undefined,

    // Account fields
    username: sanitizeString(data.username).toLowerCase(),
    temporaryPassword: data.temporaryPassword // Don't sanitize password
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get commercial full name
 */
export const getCommercialFullName = (commercial: Commercial): string => {
  return `${commercial.firstName} ${commercial.lastName}`;
};

/**
 * Calculate commercial performance score
 */
export const calculatePerformanceScore = (commercial: Commercial): number => {
  const { performance } = commercial;
  const salesScore = performance.salesAchievementRate;
  const clientScore = performance.clientRetentionRate;
  const conversionScore = performance.conversionRate;
  
  return (salesScore + clientScore + conversionScore) / 3;
};

/**
 * Get commercial performance rating
 */
export const getPerformanceRating = (commercial: Commercial): 'low' | 'medium' | 'high' => {
  const score = calculatePerformanceScore(commercial);
  
  if (score >= 80) return 'high';
  if (score >= 60) return 'medium';
  return 'low';
};

/**
 * Check if commercial is meeting targets
 */
export const isMeetingTargets = (commercial: Commercial): boolean => {
  return commercial.performance.salesAchievementRate >= 100;
};

/**
 * Calculate commission earned
 */
export const calculateCommissionEarned = (commercial: Commercial): number => {
  const { totalSales } = commercial.performance;
  const { commissionRate } = commercial.compensation;
  
  return (totalSales * commissionRate) / 100;
};

/**
 * Get next performance review date
 */
export const getNextReviewDate = (commercial: Commercial): Timestamp | null => {
  return commercial.compensation.nextReviewDate || null;
};

// ============================================================================
// ACCOUNT MANAGEMENT UTILITIES
// ============================================================================

/**
 * Check if commercial account is active and can login
 */
export const canCommercialAccountLogin = (account: CommercialAccount): boolean => {
  if (account.accountStatus !== AccountStatus.ACTIVE) return false;
  if (account.lockedUntil && account.lockedUntil.toDate() > new Date()) return false;
  return true;
};

/**
 * Check if commercial account needs password change
 */
export const needsCommercialPasswordChange = (account: CommercialAccount): boolean => {
  return account.isFirstLogin || account.mustChangePassword;
};

/**
 * Generate default permissions for commercial
 */
export const getDefaultCommercialPermissions = (): CommercialMobilePermission[] => {
  return [
    CommercialMobilePermission.VIEW_CLIENTS,
    CommercialMobilePermission.EDIT_CLIENTS,
    CommercialMobilePermission.CREATE_ORDERS,
    CommercialMobilePermission.VIEW_PRODUCTS,
    CommercialMobilePermission.VIEW_REPORTS,
    CommercialMobilePermission.SYNC_OFFLINE
  ];
};

/**
 * Create default commercial account settings
 */
export const createDefaultCommercialAccountSettings = (
  username: string,
  email: string,
  temporaryPassword: string,
  createdBy: string
): CommercialAccount => {
  return {
    username: username.toLowerCase(),
    email: email.toLowerCase(),
    temporaryPassword,
    accountStatus: AccountStatus.PENDING_ACTIVATION,
    isFirstLogin: true,
    mustChangePassword: true,
    failedLoginAttempts: 0,
    permissions: getDefaultCommercialPermissions(),
    canAccessOffline: true,
    maxOfflineDays: 7,
    registeredDevices: [],
    requireBiometric: false,
    sessionTimeout: 480, // 8 hours
    allowMultipleDevices: true, // Commercials often need multiple devices
    canViewAllClients: false,
    canEditPricing: false,
    maxDiscountPercentage: 5, // 5% default discount limit
    requireApprovalAbove: 10000, // Orders above 10k need approval
    createdBy,
    createdDate: Timestamp.now()
  };
};

/**
 * Check if commercial can approve discount
 */
export const canApproveDiscount = (account: CommercialAccount, discountPercentage: number): boolean => {
  return account.permissions.includes(CommercialMobilePermission.APPROVE_DISCOUNTS) &&
         discountPercentage <= account.maxDiscountPercentage;
};

/**
 * Check if order needs approval
 */
export const orderNeedsApproval = (account: CommercialAccount, orderAmount: number): boolean => {
  return orderAmount > account.requireApprovalAbove;
};

/**
 * Check if commercial can access client
 */
export const canAccessClient = (account: CommercialAccount, clientId: string, assignedClients: string[]): boolean => {
  if (account.canViewAllClients) return true;
  return assignedClients.includes(clientId);
};
