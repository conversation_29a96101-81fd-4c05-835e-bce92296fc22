import { auth } from './config';
import { signInWithEmailAndPassword } from 'firebase/auth';

// Fonction de test pour vérifier la configuration Firebase
export const testFirebaseConnection = async () => {
  console.log("=== Test de la configuration Firebase ===");
  
  // Vérifier si Firebase Auth est initialisé
  console.log("Firebase Auth instance:", auth);
  console.log("Firebase Auth config:", auth.config);
  
  // Tester la connexion avec des identifiants de test
  const testEmail = "<EMAIL>";
  const testPassword = "testpassword";
  
  try {
    console.log("Tentative de connexion avec des identifiants de test...");
    await signInWithEmailAndPassword(auth, testEmail, testPassword);
    console.log("✅ Connexion de test réussie");
  } catch (error) {
    console.log("❌ Erreur de connexion attendue pour les identifiants de test:", error);
    
    // Vérifier que l'erreur est bien une erreur Firebase Auth
    if (error && typeof error === 'object' && 'code' in error) {
      console.log("✅ Code d'erreur Firebase Auth reçu:", (error as { code: string }).code);
    } else {
      console.log("❌ Format d'erreur inattendu:", error);
    }
  }
  
  console.log("=== Fin du test ===");
};

// Fonction pour vérifier l'état de la connexion réseau
export const checkNetworkStatus = () => {
  console.log("=== Vérification du statut réseau ===");
  console.log("Navigator online:", navigator.onLine);
  
  // Vérifier si l'API Network Information est disponible
  const connection = (navigator as { connection?: { effectiveType?: string; downlink?: number } }).connection;
  if (connection) {
    console.log("Connection type:", connection.effectiveType);
    console.log("Connection downlink:", connection.downlink);
  }
  
  // Test de ping vers Google
  fetch('https://www.google.com/favicon.ico', { mode: 'no-cors' })
    .then(() => console.log("✅ Connexion internet fonctionnelle"))
    .catch(() => console.log("❌ Problème de connexion internet"));
};

export default { testFirebaseConnection, checkNetworkStatus };
