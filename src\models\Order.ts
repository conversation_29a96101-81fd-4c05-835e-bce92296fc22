import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  OrderStatus,
  Priority,
  Address,
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString
} from './common';

// ============================================================================
// ORDER INTERFACES
// ============================================================================

/**
 * Order type enum
 */
export enum OrderType {
  STANDARD = 'standard',
  EXPRESS = 'express',
  BULK = 'bulk',
  SUBSCRIPTION = 'subscription',
  RETURN = 'return',
  EXCHANGE = 'exchange',
  SAMPLE = 'sample'
}

/**
 * Payment status enum
 */
export enum PaymentStatus {
  PENDING = 'pending',
  AUTHORIZED = 'authorized',
  CAPTURED = 'captured',
  PAID = 'paid',
  PARTIALLY_PAID = 'partially_paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
  CANCELLED = 'cancelled'
}

/**
 * Shipping method enum
 */
export enum ShippingMethod {
  STANDARD = 'standard',
  EXPRESS = 'express',
  OVERNIGHT = 'overnight',
  PICKUP = 'pickup',
  DELIVERY = 'delivery',
  DIGITAL = 'digital'
}

/**
 * Order source enum
 */
export enum OrderSource {
  WEBSITE = 'website',
  MOBILE_APP = 'mobile_app',
  PHONE = 'phone',
  EMAIL = 'email',
  IN_PERSON = 'in_person',
  MARKETPLACE = 'marketplace',
  SOCIAL_MEDIA = 'social_media',
  PARTNER = 'partner'
}

/**
 * Order line item interface
 */
export interface OrderLineItem {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  variantId?: string;
  variantName?: string;
  
  // Pricing
  unitPrice: number;
  originalPrice: number;
  discountAmount: number;
  discountPercentage: number;
  taxAmount: number;
  taxRate: number;
  
  // Quantity
  quantity: number;
  quantityShipped: number;
  quantityReturned: number;
  quantityCancelled: number;
  
  // Totals
  lineTotal: number;
  lineTotalWithTax: number;
  
  // Product details at time of order
  productSnapshot: {
    name: string;
    description: string;
    imageUrl?: string;
    weight?: number;
    dimensions?: {
      length: number;
      width: number;
      height: number;
    };
  };
  
  // Status
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'returned' | 'cancelled';
  notes?: string;
}

/**
 * Order discount interface
 */
export interface OrderDiscount {
  id: string;
  type: 'percentage' | 'fixed_amount' | 'free_shipping';
  code?: string;
  name: string;
  amount: number;
  appliedTo: 'order' | 'shipping' | 'specific_items';
  itemIds?: string[]; // For item-specific discounts
}

/**
 * Order tax interface
 */
export interface OrderTax {
  id: string;
  name: string;
  rate: number;
  amount: number;
  type: 'sales_tax' | 'vat' | 'gst' | 'other';
  jurisdiction?: string;
}

/**
 * Order shipping information
 */
export interface OrderShipping {
  method: ShippingMethod;
  carrier?: string;
  service?: string;
  cost: number;
  estimatedDeliveryDate?: Timestamp;
  actualDeliveryDate?: Timestamp;
  
  // Tracking
  trackingNumber?: string;
  trackingUrl?: string;
  
  // Address
  shippingAddress: Address;
  
  // Package details
  packages?: {
    id: string;
    trackingNumber?: string;
    weight: number;
    dimensions: {
      length: number;
      width: number;
      height: number;
    };
    items: string[]; // Order line item IDs
    shippedDate?: Timestamp;
    deliveredDate?: Timestamp;
  }[];
}

/**
 * Order payment information
 */
export interface OrderPayment {
  method: 'credit_card' | 'debit_card' | 'bank_transfer' | 'paypal' | 'cash' | 'check' | 'other';
  status: PaymentStatus;
  
  // Amounts
  totalAmount: number;
  paidAmount: number;
  refundedAmount: number;
  pendingAmount: number;
  
  // Payment details
  transactionId?: string;
  paymentGateway?: string;
  paymentReference?: string;
  
  // Card details (masked)
  cardLast4?: string;
  cardBrand?: string;
  cardExpiry?: string;
  
  // Dates
  authorizedDate?: Timestamp;
  capturedDate?: Timestamp;
  paidDate?: Timestamp;
  
  // Billing
  billingAddress: Address;
  
  // Payment history
  transactions: {
    id: string;
    type: 'authorization' | 'capture' | 'refund' | 'void';
    amount: number;
    status: 'success' | 'failed' | 'pending';
    transactionId: string;
    date: Timestamp;
    notes?: string;
  }[];
}

/**
 * Order fulfillment information
 */
export interface OrderFulfillment {
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  warehouseId?: string;
  warehouseName?: string;
  
  // Dates
  processingStartDate?: Timestamp;
  shippedDate?: Timestamp;
  deliveredDate?: Timestamp;
  
  // Staff
  processedBy?: string;
  packedBy?: string;
  shippedBy?: string;
  
  // Notes
  packingNotes?: string;
  shippingNotes?: string;
  deliveryNotes?: string;
}

/**
 * Main Order interface
 */
export interface Order extends BaseEntity {
  // Basic Information
  orderNumber: string;
  type: OrderType;
  source: OrderSource;
  priority: Priority;
  
  // Customer Information
  clientId: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  
  // Commercial Information
  commercialId: string;
  commercialName: string;
  
  // Order Items
  lineItems: OrderLineItem[];
  
  // Pricing and Totals
  subtotal: number;
  discountTotal: number;
  taxTotal: number;
  shippingTotal: number;
  totalAmount: number;
  
  // Discounts and Taxes
  discounts: OrderDiscount[];
  taxes: OrderTax[];
  
  // Shipping
  shipping: OrderShipping;
  
  // Payment
  payment: OrderPayment;
  
  // Fulfillment
  fulfillment: OrderFulfillment;
  
  // Status and Dates
  status: OrderStatus;
  orderDate: Timestamp;
  requestedDeliveryDate?: Timestamp;
  promisedDeliveryDate?: Timestamp;
  completedDate?: Timestamp;
  cancelledDate?: Timestamp;
  
  // Communication
  customerNotes?: string;
  internalNotes?: string;
  
  // References
  customerReference?: string;
  poNumber?: string; // Purchase Order Number
  invoiceNumber?: string;
  
  // Flags
  isGift: boolean;
  giftMessage?: string;
  isRush: boolean;
  requiresSignature: boolean;
  
  // Metadata
  tags: string[];
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// ORDER FORM INTERFACES
// ============================================================================

/**
 * Order form data interface (for forms)
 */
export interface OrderFormData {
  orderNumber: string;
  type: OrderType;
  source: OrderSource;
  priority: Priority;
  
  // Customer
  clientId: string;
  clientName: string;
  
  // Commercial
  commercialId: string;
  
  // Items (simplified for form)
  items: {
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
  }[];
  
  // Dates
  orderDate: string; // ISO date string
  requestedDeliveryDate?: string;
  
  // Shipping
  shippingMethod: ShippingMethod;
  shippingCost: number;
  shippingAddress: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  
  // Payment
  paymentMethod: 'credit_card' | 'debit_card' | 'bank_transfer' | 'paypal' | 'cash' | 'check' | 'other';
  billingAddress: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  
  // Status
  status: OrderStatus;
  
  // Notes
  customerNotes?: string;
  internalNotes?: string;
  
  // Flags
  isGift: boolean;
  giftMessage?: string;
  isRush: boolean;
  requiresSignature: boolean;
  
  // References
  customerReference?: string;
  poNumber?: string;
  
  // Tags
  tags: string[];
}

/**
 * Order search/filter interface
 */
export interface OrderSearchFilters {
  orderNumber?: string;
  clientId?: string;
  clientName?: string;
  clientEmail?: string;
  commercialId?: string;
  type?: OrderType;
  source?: OrderSource;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  priority?: Priority;
  
  // Date ranges
  orderDateFrom?: Timestamp;
  orderDateTo?: Timestamp;
  deliveryDateFrom?: Timestamp;
  deliveryDateTo?: Timestamp;
  
  // Amount ranges
  minAmount?: number;
  maxAmount?: number;
  
  // Product filters
  productId?: string;
  productSku?: string;
  
  // Shipping
  shippingMethod?: ShippingMethod;
  trackingNumber?: string;
  
  // References
  customerReference?: string;
  poNumber?: string;
  invoiceNumber?: string;
  
  // Flags
  isGift?: boolean;
  isRush?: boolean;
  requiresSignature?: boolean;
  
  // Tags
  tags?: string[];
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateOrderData = CreateEntity<Order>;
export type UpdateOrderData = UpdateEntity<Order>;
export type OrderWithId = Order & Required<Pick<Order, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate order form data
 */
export const validateOrderData = (data: Partial<OrderFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.orderNumber || data.orderNumber.trim().length === 0) {
    errors.push({
      field: 'orderNumber',
      message: 'Le numéro de commande est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.clientId || data.clientId.trim().length === 0) {
    errors.push({
      field: 'clientId',
      message: 'Le client est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.commercialId || data.commercialId.trim().length === 0) {
    errors.push({
      field: 'commercialId',
      message: 'Le commercial est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.items || data.items.length === 0) {
    errors.push({
      field: 'items',
      message: 'Au moins un produit est requis',
      code: 'REQUIRED'
    });
  }

  // Validate items
  if (data.items) {
    data.items.forEach((item, index) => {
      if (!item.productId) {
        errors.push({
          field: `items[${index}].productId`,
          message: `Produit requis pour la ligne ${index + 1}`,
          code: 'REQUIRED'
        });
      }

      if (item.quantity <= 0) {
        errors.push({
          field: `items[${index}].quantity`,
          message: `Quantité invalide pour la ligne ${index + 1}`,
          code: 'INVALID_VALUE'
        });
      }

      if (item.unitPrice < 0) {
        errors.push({
          field: `items[${index}].unitPrice`,
          message: `Prix invalide pour la ligne ${index + 1}`,
          code: 'INVALID_VALUE'
        });
      }
    });
  }

  // Shipping validation
  if (data.shippingCost !== undefined && data.shippingCost < 0) {
    errors.push({
      field: 'shippingCost',
      message: 'Les frais de livraison ne peuvent pas être négatifs',
      code: 'INVALID_VALUE'
    });
  }

  // Address validation
  if (data.shippingAddress) {
    if (!data.shippingAddress.street || data.shippingAddress.street.trim().length === 0) {
      errors.push({
        field: 'shippingAddress.street',
        message: 'L\'adresse de livraison est requise',
        code: 'REQUIRED'
      });
    }

    if (!data.shippingAddress.city || data.shippingAddress.city.trim().length === 0) {
      errors.push({
        field: 'shippingAddress.city',
        message: 'La ville de livraison est requise',
        code: 'REQUIRED'
      });
    }

    if (!data.shippingAddress.postalCode || data.shippingAddress.postalCode.trim().length === 0) {
      errors.push({
        field: 'shippingAddress.postalCode',
        message: 'Le code postal de livraison est requis',
        code: 'REQUIRED'
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize order data before saving
 */
export const sanitizeOrderData = (data: OrderFormData): OrderFormData => {
  return {
    ...data,
    orderNumber: sanitizeString(data.orderNumber),
    clientName: sanitizeString(data.clientName),
    customerNotes: data.customerNotes ? sanitizeString(data.customerNotes) : undefined,
    internalNotes: data.internalNotes ? sanitizeString(data.internalNotes) : undefined,
    giftMessage: data.giftMessage ? sanitizeString(data.giftMessage) : undefined,
    customerReference: data.customerReference ? sanitizeString(data.customerReference) : undefined,
    poNumber: data.poNumber ? sanitizeString(data.poNumber) : undefined,
    
    // Sanitize addresses
    shippingAddress: {
      ...data.shippingAddress,
      street: sanitizeString(data.shippingAddress.street),
      city: sanitizeString(data.shippingAddress.city),
      postalCode: sanitizeString(data.shippingAddress.postalCode),
      country: sanitizeString(data.shippingAddress.country)
    },
    billingAddress: {
      ...data.billingAddress,
      street: sanitizeString(data.billingAddress.street),
      city: sanitizeString(data.billingAddress.city),
      postalCode: sanitizeString(data.billingAddress.postalCode),
      country: sanitizeString(data.billingAddress.country)
    }
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate order subtotal
 */
export const calculateOrderSubtotal = (lineItems: OrderLineItem[]): number => {
  return lineItems.reduce((total, item) => total + item.lineTotal, 0);
};

/**
 * Calculate order total
 */
export const calculateOrderTotal = (order: Partial<Order>): number => {
  const subtotal = order.subtotal || 0;
  const taxTotal = order.taxTotal || 0;
  const shippingTotal = order.shippingTotal || 0;
  const discountTotal = order.discountTotal || 0;
  
  return subtotal + taxTotal + shippingTotal - discountTotal;
};

/**
 * Generate order number
 */
export const generateOrderNumber = (prefix: string = 'ORD'): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Check if order can be cancelled
 */
export const canCancelOrder = (order: Order): boolean => {
  return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status);
};

/**
 * Check if order can be modified
 */
export const canModifyOrder = (order: Order): boolean => {
  return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status);
};

/**
 * Get order status display text
 */
export const getOrderStatusText = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.PENDING]: 'En attente',
    [OrderStatus.CONFIRMED]: 'Confirmée',
    [OrderStatus.PROCESSING]: 'En traitement',
    [OrderStatus.SHIPPED]: 'Expédiée',
    [OrderStatus.DELIVERED]: 'Livrée',
    [OrderStatus.CANCELLED]: 'Annulée',
    [OrderStatus.REFUNDED]: 'Remboursée'
  };
  
  return statusMap[status] || status;
};

/**
 * Check if order is overdue
 */
export const isOrderOverdue = (order: Order): boolean => {
  if (!order.promisedDeliveryDate) return false;
  return order.promisedDeliveryDate.toDate() < new Date() && 
         order.status !== OrderStatus.DELIVERED;
};

/**
 * Calculate order profit
 */
export const calculateOrderProfit = (order: Order): number => {
  // This would need product cost information
  // Placeholder implementation
  return order.totalAmount * 0.2; // Assume 20% profit margin
};
