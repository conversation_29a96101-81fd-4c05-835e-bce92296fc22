import React, { useState } from "react";
import { Plus, Search, Edit, Trash2, MapPin, Calendar } from "lucide-react";
import Modal from "../components/ui/Modal";

interface Merchandizer {
  id: number;
  name: string;
  email: string;
  phone: string;
  territory: string;
  stores: number;
  lastVisit: string;
  performance: number;
  status: "active" | "inactive";
}

const MerchandizerManagement: React.FC = () => {
  const [merchandizers, setMerchandizers] = useState<Merchandizer[]>([
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8901",
      territory: "Downtown District",
      stores: 12,
      lastVisit: "2024-01-15",
      performance: 92,
      status: "active",
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8902",
      territory: "Shopping Centers",
      stores: 8,
      lastVisit: "2024-01-14",
      performance: 87,
      status: "active",
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8903",
      territory: "Suburban Areas",
      stores: 15,
      lastVisit: "2024-01-10",
      performance: 78,
      status: "inactive",
    },
    {
      id: 4,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8904",
      territory: "Mall Network",
      stores: 6,
      lastVisit: "2024-01-16",
      performance: 95,
      status: "active",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingMerchandizer, setEditingMerchandizer] =
    useState<Merchandizer | null>(null);

  const filteredMerchandizers = merchandizers.filter(
    (merchandizer) =>
      merchandizer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      merchandizer.territory.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (merchandizer: Merchandizer) => {
    setEditingMerchandizer(merchandizer);
    setShowModal(true);
  };

  const handleDelete = (id: number) => {
    setMerchandizers(merchandizers.filter((m) => m.id !== id));
  };

  const handleAdd = () => {
    setEditingMerchandizer(null);
    setShowModal(true);
  };

  const getPerformanceColor = (performance: number) => {
    if (performance >= 90) return "text-green-600";
    if (performance >= 80) return "text-yellow-600";
    return "text-red-600";
  };

  const getPerformanceBg = (performance: number) => {
    if (performance >= 90) return "bg-green-100";
    if (performance >= 80) return "bg-yellow-100";
    return "bg-red-100";
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Gestion des Merchandiseurs
        </h1>
        <button
          onClick={handleAdd}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Ajouter un Merchandiseur</span>
        </button>
      </div>

      {/* Search Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Rechercher des merchandiseurs..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Merchandizers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMerchandizers.map((merchandizer) => (
          <div
            key={merchandizer.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {merchandizer.name}
                </h3>
                <p className="text-sm text-gray-600">{merchandizer.email}</p>
                <p className="text-sm text-gray-600">{merchandizer.phone}</p>
              </div>
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  merchandizer.status === "active"
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {merchandizer.status === "active" ? "Actif" : "Inactif"}
              </span>
            </div>

            <div className="space-y-3 mb-4">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="h-4 w-4 mr-2" />
                <span>{merchandizer.territory}</span>
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                <span>Dernière visite: {merchandizer.lastVisit}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {merchandizer.stores}
                </div>
                <div className="text-xs text-gray-500">Magasins</div>
              </div>
              <div className="text-center">
                <div
                  className={`text-2xl font-bold ${getPerformanceColor(
                    merchandizer.performance
                  )}`}
                >
                  {merchandizer.performance}%
                </div>
                <div className="text-xs text-gray-500">Performance</div>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Performance</span>
                <span>{merchandizer.performance}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${getPerformanceBg(
                    merchandizer.performance
                  )} ${getPerformanceColor(merchandizer.performance).replace(
                    "text-",
                    "bg-"
                  )}`}
                  style={{ width: `${merchandizer.performance}%` }}
                ></div>
              </div>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleEdit(merchandizer)}
                className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center space-x-1"
              >
                <Edit className="h-4 w-4" />
                <span>Modifier</span>
              </button>
              <button
                onClick={() => handleDelete(merchandizer.id)}
                className="bg-red-50 text-red-600 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {showModal && (
        <Modal
          title={editingMerchandizer ? "Edit Merchandizer" : "Add Merchandizer"}
          onClose={() => setShowModal(false)}
        >
          <form className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <input
                type="text"
                defaultValue={editingMerchandizer?.name || ""}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  defaultValue={editingMerchandizer?.email || ""}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  defaultValue={editingMerchandizer?.phone || ""}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Territory
              </label>
              <input
                type="text"
                defaultValue={editingMerchandizer?.territory || ""}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Stores
                </label>
                <input
                  type="number"
                  defaultValue={editingMerchandizer?.stores || ""}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  defaultValue={editingMerchandizer?.status || "active"}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowModal(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingMerchandizer ? "Update" : "Add"} Merchandizer
              </button>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
};

export default MerchandizerManagement;
