import React, { useState, useEffect } from "react";
import { X, Save, Loader2, Upload, Download, FileText } from "lucide-react";
import { Catalogue } from "../../firebase/services";
import { useApp } from "../../hooks/useApp";

interface CatalogueModalProps {
  isOpen: boolean;
  onClose: () => void;
  catalogue?: Catalogue | null;
  mode: "create" | "edit" | "view";
}

const CatalogueModal: React.FC<CatalogueModalProps> = ({
  isOpen,
  onClose,
  catalogue,
  mode,
}) => {
  const { addCatalogue, updateCatalogue } = useApp();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    fileUrl: "",
    fileSize: "",
    downloads: 0,
    status: "active" as "active" | "inactive" | "draft",
  });

  const categories = [
    "Produits Électroniques",
    "Mode & Vêtements",
    "Maison & Décoration",
    "Sport & Loisirs",
    "Beauté & Cosmétiques",
    "Automobile",
    "Alimentation & Boissons",
    "Santé & Bien-être",
    "Livres & Médias",
    "Jouets & Enfants",
    "Jardin & Extérieur",
    "Professionnel & Industrie",
    "Général",
  ];

  useEffect(() => {
    if (catalogue && (mode === "edit" || mode === "view")) {
      setFormData({
        name: catalogue.name || "",
        description: catalogue.description || "",
        category: catalogue.category || "",
        fileUrl: catalogue.fileUrl || "",
        fileSize: catalogue.fileSize || "",
        downloads: catalogue.downloads || 0,
        status: catalogue.status || "active",
      });
    } else {
      setFormData({
        name: "",
        description: "",
        category: "",
        fileUrl: "",
        fileSize: "",
        downloads: 0,
        status: "draft",
      });
    }
    setError(null);
  }, [catalogue, mode, isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "number" ? parseInt(value) || 0 : value,
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setError("Le nom du catalogue est requis");
      return false;
    }
    if (!formData.description.trim()) {
      setError("La description est requise");
      return false;
    }
    if (!formData.category.trim()) {
      setError("La catégorie est requise");
      return false;
    }
    if (formData.fileUrl && !isValidUrl(formData.fileUrl)) {
      setError("L'URL du fichier doit être valide");
      return false;
    }
    return true;
  };

  const isValidUrl = (string: string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Here you would typically upload the file to Firebase Storage
      // and get the download URL
      const fileSizeInMB = (file.size / (1024 * 1024)).toFixed(2);
      setFormData((prev) => ({
        ...prev,
        fileSize: `${fileSizeInMB} MB`,
      }));

      // Placeholder for file upload logic
      console.log("File selected:", file.name, "Size:", fileSizeInMB, "MB");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mode === "view") return;

    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      // Note: You'll need to implement these methods in your app context
      // For now, this is a placeholder
      console.log("Catalogue data:", formData);

      // if (mode === 'create') {
      //   await addCatalogue(formData);
      // } else if (mode === 'edit' && catalogue?.id) {
      //   await updateCatalogue(catalogue.id, formData);
      // }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Une erreur est survenue");
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const isReadOnly = mode === "view";
  const title =
    mode === "create"
      ? "Nouveau Catalogue"
      : mode === "edit"
      ? "Modifier Catalogue"
      : "Détails Catalogue";

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
        >
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nom du catalogue *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
                placeholder="ex: Catalogue Printemps 2024"
              />
            </div>

            <div className="md:col-span-2">
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                disabled={isReadOnly}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
                placeholder="Description détaillée du catalogue..."
              />
            </div>

            <div>
              <label
                htmlFor="category"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Catégorie *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                required
              >
                <option value="">Sélectionner une catégorie</option>
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label
                htmlFor="status"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Statut
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="draft">Brouillon</option>
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label
                htmlFor="fileUrl"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                URL du fichier
              </label>
              <div className="flex">
                <input
                  type="url"
                  id="fileUrl"
                  name="fileUrl"
                  value={formData.fileUrl}
                  onChange={handleInputChange}
                  disabled={isReadOnly}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="https://example.com/catalogue.pdf"
                />
                {!isReadOnly && (
                  <label className="px-3 py-2 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer flex items-center">
                    <Upload className="h-4 w-4" />
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      className="hidden"
                    />
                  </label>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Formats acceptés: PDF, DOC, DOCX, JPG, PNG
              </p>
            </div>

            <div>
              <label
                htmlFor="fileSize"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Taille du fichier
              </label>
              <input
                type="text"
                id="fileSize"
                name="fileSize"
                value={formData.fileSize}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                placeholder="ex: 2.5 MB"
              />
            </div>

            <div>
              <label
                htmlFor="downloads"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Nombre de téléchargements
              </label>
              <input
                type="number"
                id="downloads"
                name="downloads"
                value={formData.downloads}
                onChange={handleInputChange}
                disabled={true}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-100"
              />
            </div>

            {formData.fileUrl && (
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Aperçu du fichier
                </label>
                <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-8 w-8 text-blue-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {formData.name || "Catalogue"}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formData.fileSize || "Taille inconnue"}
                      </p>
                    </div>
                    {formData.fileUrl && (
                      <a
                        href={formData.fileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                      >
                        <Download className="h-4 w-4" />
                        <span>Télécharger</span>
                      </a>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {!isReadOnly && (
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              >
                Annuler
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                {loading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{mode === "create" ? "Créer" : "Sauvegarder"}</span>
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default CatalogueModal;
