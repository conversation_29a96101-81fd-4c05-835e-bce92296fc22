import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  EntityStatus,
  Priority,
  ContactInfo, 
  Address, 
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// MERCHANDIZER INTERFACES
// ============================================================================

/**
 * Merchandizer type enum
 */
export enum MerchandizerType {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
  FREELANCE = 'freelance',
  AGENCY = 'agency',
  PARTNER = 'partner'
}

/**
 * Visit type enum
 */
export enum VisitType {
  ROUTINE = 'routine',
  AUDIT = 'audit',
  SETUP = 'setup',
  TRAINING = 'training',
  PROBLEM_SOLVING = 'problem_solving',
  INVENTORY = 'inventory',
  PROMOTION = 'promotion'
}

/**
 * Store type enum
 */
export enum StoreType {
  SUPERMARKET = 'supermarket',
  CONVENIENCE = 'convenience',
  PHARMACY = 'pharmacy',
  DEPARTMENT = 'department',
  SPECIALTY = 'specialty',
  ONLINE = 'online',
  WAREHOUSE = 'warehouse'
}

/**
 * Store information interface
 */
export interface Store {
  id: string;
  name: string;
  type: StoreType;
  address: Address;
  manager: string;
  phone: string;
  email?: string;
  
  // Business details
  chainName?: string;
  storeNumber?: string;
  size: 'small' | 'medium' | 'large' | 'xl';
  footTraffic: 'low' | 'medium' | 'high' | 'very_high';
  
  // Relationship
  priority: Priority;
  contractType: 'exclusive' | 'non_exclusive' | 'trial';
  contractStartDate: Timestamp;
  contractEndDate?: Timestamp;
  
  // Performance
  monthlyRevenue: number;
  averageOrderValue: number;
  lastOrderDate?: Timestamp;
  
  // Status
  status: EntityStatus;
  isActive: boolean;
  
  // Metadata
  notes?: string;
  tags: string[];
}

/**
 * Visit report interface
 */
export interface VisitReport {
  id: string;
  storeId: string;
  storeName: string;
  visitType: VisitType;
  visitDate: Timestamp;
  duration: number; // minutes
  
  // Objectives and outcomes
  objectives: string[];
  achievements: string[];
  issues: string[];
  recommendations: string[];
  
  // Inventory and display
  inventoryCheck: {
    productId: string;
    productName: string;
    currentStock: number;
    recommendedStock: number;
    outOfStock: boolean;
    displayQuality: 'excellent' | 'good' | 'fair' | 'poor';
  }[];
  
  // Competitive analysis
  competitorAnalysis?: {
    competitor: string;
    products: string[];
    pricing: string;
    placement: string;
    promotion: string;
    notes: string;
  }[];
  
  // Photos and evidence
  photos: {
    id: string;
    url: string;
    caption: string;
    category: 'display' | 'inventory' | 'competitor' | 'issue' | 'other';
  }[];
  
  // Ratings and scores
  storeRating: number; // 1-10
  displayQuality: number; // 1-10
  staffCooperation: number; // 1-10
  
  // Follow-up
  followUpRequired: boolean;
  followUpDate?: Timestamp;
  followUpNotes?: string;
  
  // Status
  status: 'draft' | 'submitted' | 'reviewed' | 'approved';
  submittedDate?: Timestamp;
  reviewedBy?: string;
  reviewedDate?: Timestamp;
  
  // Metadata
  notes?: string;
  tags: string[];
}

/**
 * Merchandizer performance metrics
 */
export interface MerchandizerPerformance {
  // Visit metrics
  totalVisits: number;
  plannedVisits: number;
  completedVisits: number;
  visitCompletionRate: number; // percentage
  averageVisitDuration: number; // minutes
  
  // Store metrics
  totalStores: number;
  activeStores: number;
  newStoresAcquired: number;
  storesLost: number;
  storeRetentionRate: number; // percentage
  
  // Revenue metrics
  totalRevenue: number;
  revenueTarget: number;
  revenueAchievementRate: number; // percentage
  averageRevenuePerStore: number;
  
  // Quality metrics
  averageStoreRating: number;
  averageDisplayQuality: number;
  customerSatisfactionScore: number;
  
  // Efficiency metrics
  visitsPerDay: number;
  revenuePerVisit: number;
  costPerVisit: number;
  
  // Period
  periodStart: Timestamp;
  periodEnd: Timestamp;
  lastUpdated: Timestamp;
}

/**
 * Merchandizer schedule interface
 */
export interface MerchandizerSchedule {
  id: string;
  date: Timestamp;
  storeId: string;
  storeName: string;
  visitType: VisitType;
  plannedStartTime: string; // HH:MM format
  plannedEndTime: string;
  actualStartTime?: string;
  actualEndTime?: string;
  
  // Status
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'rescheduled';
  
  // Details
  objectives: string[];
  priority: Priority;
  estimatedDuration: number; // minutes
  
  // Travel
  travelTime: number; // minutes
  travelDistance: number; // km
  
  // Notes
  notes?: string;
  cancellationReason?: string;
  rescheduleReason?: string;
}

/**
 * Main Merchandizer interface
 */
export interface Merchandizer extends BaseEntity {
  // Personal Information
  firstName: string;
  lastName: string;
  name: string; // Full name for display
  email: string;
  phone: string;
  mobile?: string;
  
  // Professional Information
  employeeId?: string;
  type: MerchandizerType;
  department?: string;
  managerId?: string;
  managerName?: string;
  
  // Territory and Assignment
  territory: string;
  regions: string[];
  assignedStores: string[];
  stores: Store[];
  
  // Performance and Metrics
  performance: MerchandizerPerformance;
  
  // Schedule and Availability
  schedule: MerchandizerSchedule[];
  workingHours: {
    monday: { start: string; end: string; available: boolean; };
    tuesday: { start: string; end: string; available: boolean; };
    wednesday: { start: string; end: string; available: boolean; };
    thursday: { start: string; end: string; available: boolean; };
    friday: { start: string; end: string; available: boolean; };
    saturday?: { start: string; end: string; available: boolean; };
    sunday?: { start: string; end: string; available: boolean; };
  };
  
  // Contact and Address
  address?: Address;
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  
  // Employment Details (if internal)
  hireDate?: Timestamp;
  contractStartDate?: Timestamp;
  contractEndDate?: Timestamp;
  
  // Compensation (if applicable)
  compensation?: {
    type: 'salary' | 'hourly' | 'commission' | 'per_visit';
    amount: number;
    currency: string;
    commissionRate?: number; // percentage
    bonusStructure?: {
      targetBonus: number;
      achievementThresholds: {
        threshold: number;
        bonusMultiplier: number;
      }[];
    };
  };
  
  // Equipment and Resources
  equipment: {
    vehicle?: {
      type: string;
      licensePlate: string;
      insuranceExpiry: Timestamp;
    };
    mobileDevice?: {
      type: string;
      imei: string;
      phoneNumber: string;
    };
    tools: string[];
  };
  
  // Certifications and Training
  certifications: {
    name: string;
    issuer: string;
    dateObtained: Timestamp;
    expiryDate?: Timestamp;
    credentialId?: string;
  }[];
  
  // Status and Metadata
  status: EntityStatus;
  availability: 'available' | 'busy' | 'on_leave' | 'unavailable';
  lastVisitDate?: Timestamp;
  nextScheduledVisit?: Timestamp;
  
  // Metadata
  notes?: string;
  tags: string[];
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// MERCHANDIZER FORM INTERFACES
// ============================================================================

/**
 * Merchandizer form data interface (for forms)
 */
export interface MerchandizerFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  mobile?: string;
  employeeId?: string;
  type: MerchandizerType;
  department?: string;
  territory: string;
  regions: string[];
  managerId?: string;
  
  // Employment
  hireDate?: string; // ISO date string
  contractStartDate?: string;
  contractEndDate?: string;
  
  // Compensation
  compensationType: 'salary' | 'hourly' | 'commission' | 'per_visit';
  compensationAmount: number;
  commissionRate?: number;
  
  // Performance targets
  monthlyVisitTarget: number;
  monthlyRevenueTarget: number;
  storeTarget: number;
  
  // Status
  status: EntityStatus;
  availability: 'available' | 'busy' | 'on_leave' | 'unavailable';
  
  // Notes
  notes?: string;
  tags: string[];
}

/**
 * Merchandizer search/filter interface
 */
export interface MerchandizerSearchFilters {
  name?: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  type?: MerchandizerType;
  department?: string;
  territory?: string;
  managerId?: string;
  status?: EntityStatus;
  availability?: 'available' | 'busy' | 'on_leave' | 'unavailable';
  tags?: string[];
  
  // Performance filters
  minPerformanceScore?: number;
  maxPerformanceScore?: number;
  minStores?: number;
  maxStores?: number;
  
  // Date filters
  hiredAfter?: Timestamp;
  hiredBefore?: Timestamp;
  lastVisitAfter?: Timestamp;
  lastVisitBefore?: Timestamp;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateMerchandizerData = CreateEntity<Merchandizer>;
export type UpdateMerchandizerData = UpdateEntity<Merchandizer>;
export type MerchandizerWithId = Merchandizer & Required<Pick<Merchandizer, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate merchandizer form data
 */
export const validateMerchandizerData = (data: Partial<MerchandizerFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.firstName || data.firstName.trim().length === 0) {
    errors.push({
      field: 'firstName',
      message: 'Le prénom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.lastName || data.lastName.trim().length === 0) {
    errors.push({
      field: 'lastName',
      message: 'Le nom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push({
      field: 'email',
      message: 'L\'email est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Format d\'email invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.phone || data.phone.trim().length === 0) {
    errors.push({
      field: 'phone',
      message: 'Le téléphone est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.phone)) {
    errors.push({
      field: 'phone',
      message: 'Format de téléphone invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.territory || data.territory.trim().length === 0) {
    errors.push({
      field: 'territory',
      message: 'Le territoire est requis',
      code: 'REQUIRED'
    });
  }

  // Business logic validations
  if (data.compensationAmount !== undefined && data.compensationAmount < 0) {
    errors.push({
      field: 'compensationAmount',
      message: 'Le montant de compensation ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.commissionRate !== undefined && (data.commissionRate < 0 || data.commissionRate > 100)) {
    errors.push({
      field: 'commissionRate',
      message: 'Le taux de commission doit être entre 0 et 100%',
      code: 'INVALID_VALUE'
    });
  }

  if (data.monthlyVisitTarget !== undefined && data.monthlyVisitTarget < 0) {
    errors.push({
      field: 'monthlyVisitTarget',
      message: 'L\'objectif de visites mensuelles ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.monthlyRevenueTarget !== undefined && data.monthlyRevenueTarget < 0) {
    errors.push({
      field: 'monthlyRevenueTarget',
      message: 'L\'objectif de revenus mensuels ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.storeTarget !== undefined && data.storeTarget < 0) {
    errors.push({
      field: 'storeTarget',
      message: 'L\'objectif de magasins ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize merchandizer data before saving
 */
export const sanitizeMerchandizerData = (data: MerchandizerFormData): MerchandizerFormData => {
  return {
    ...data,
    firstName: sanitizeString(data.firstName),
    lastName: sanitizeString(data.lastName),
    email: data.email.toLowerCase().trim(),
    phone: data.phone.replace(/[\s\-\(\)]/g, ''),
    mobile: data.mobile ? data.mobile.replace(/[\s\-\(\)]/g, '') : undefined,
    employeeId: data.employeeId ? sanitizeString(data.employeeId) : undefined,
    department: data.department ? sanitizeString(data.department) : undefined,
    territory: sanitizeString(data.territory),
    notes: data.notes ? sanitizeString(data.notes) : undefined
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get merchandizer full name
 */
export const getMerchandizerFullName = (merchandizer: Merchandizer): string => {
  return `${merchandizer.firstName} ${merchandizer.lastName}`;
};

/**
 * Calculate merchandizer performance score
 */
export const calculateMerchandizerPerformanceScore = (merchandizer: Merchandizer): number => {
  const { performance } = merchandizer;
  const visitScore = performance.visitCompletionRate;
  const revenueScore = performance.revenueAchievementRate;
  const qualityScore = (performance.averageStoreRating / 10) * 100;
  const retentionScore = performance.storeRetentionRate;
  
  return (visitScore + revenueScore + qualityScore + retentionScore) / 4;
};

/**
 * Get merchandizer performance rating
 */
export const getMerchandizerPerformanceRating = (merchandizer: Merchandizer): 'low' | 'medium' | 'high' => {
  const score = calculateMerchandizerPerformanceScore(merchandizer);
  
  if (score >= 80) return 'high';
  if (score >= 60) return 'medium';
  return 'low';
};

/**
 * Check if merchandizer is meeting targets
 */
export const isMeetingTargets = (merchandizer: Merchandizer): boolean => {
  const { performance } = merchandizer;
  return performance.visitCompletionRate >= 90 && 
         performance.revenueAchievementRate >= 100;
};

/**
 * Calculate total stores managed
 */
export const getTotalStoresManaged = (merchandizer: Merchandizer): number => {
  return merchandizer.stores.filter(store => store.isActive).length;
};

/**
 * Get next scheduled visit
 */
export const getNextScheduledVisit = (merchandizer: Merchandizer): MerchandizerSchedule | null => {
  const now = new Date();
  const upcomingVisits = merchandizer.schedule
    .filter(visit => visit.date.toDate() > now && visit.status === 'scheduled')
    .sort((a, b) => a.date.toMillis() - b.date.toMillis());
  
  return upcomingVisits.length > 0 ? upcomingVisits[0] : null;
};

/**
 * Calculate average visit duration
 */
export const calculateAverageVisitDuration = (merchandizer: Merchandizer): number => {
  const completedVisits = merchandizer.schedule.filter(visit => visit.status === 'completed');
  if (completedVisits.length === 0) return 0;
  
  const totalDuration = completedVisits.reduce((sum, visit) => {
    if (visit.actualStartTime && visit.actualEndTime) {
      const start = new Date(`2000-01-01 ${visit.actualStartTime}`);
      const end = new Date(`2000-01-01 ${visit.actualEndTime}`);
      return sum + (end.getTime() - start.getTime()) / (1000 * 60); // minutes
    }
    return sum;
  }, 0);
  
  return totalDuration / completedVisits.length;
};
