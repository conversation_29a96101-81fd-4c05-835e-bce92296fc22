import React, { useState } from "react";
import {
  Plus,
  Search,
  Download,
  Trash2,
  Upload,
  FileText,
  Calendar,
} from "lucide-react";
import Modal from "../components/ui/Modal";

interface Catalogue {
  id: number;
  name: string;
  description: string;
  category: string;
  uploadDate: string;
  fileSize: string;
  downloads: number;
  status: "active" | "inactive" | "draft";
}

const CatalogueManagement: React.FC = () => {
  const [catalogues, setCatalogues] = useState<Catalogue[]>([
    {
      id: 1,
      name: "Spring Collection 2024",
      description: "Latest spring products and seasonal items",
      category: "Seasonal",
      uploadDate: "2024-01-15",
      fileSize: "2.4 MB",
      downloads: 145,
      status: "active",
    },
    {
      id: 2,
      name: "Electronics Catalogue",
      description: "Complete range of electronic products",
      category: "Electronics",
      uploadDate: "2024-01-12",
      fileSize: "3.1 MB",
      downloads: 267,
      status: "active",
    },
    {
      id: 3,
      name: "Premium Products Guide",
      description: "High-end and luxury product showcase",
      category: "Premium",
      uploadDate: "2024-01-08",
      fileSize: "1.8 MB",
      downloads: 89,
      status: "draft",
    },
    {
      id: 4,
      name: "Budget-Friendly Options",
      description: "Affordable products for cost-conscious clients",
      category: "Budget",
      uploadDate: "2024-01-05",
      fileSize: "1.2 MB",
      downloads: 203,
      status: "inactive",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingCatalogue, setEditingCatalogue] = useState<Catalogue | null>(
    null
  );

  const filteredCatalogues = catalogues.filter(
    (catalogue) =>
      catalogue.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      catalogue.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleEdit = (catalogue: Catalogue) => {
    setEditingCatalogue(catalogue);
    setShowModal(true);
  };

  const handleDelete = (id: number) => {
    setCatalogues(catalogues.filter((c) => c.id !== id));
  };

  const handleAdd = () => {
    setEditingCatalogue(null);
    setShowModal(true);
  };

  const handleDownload = (catalogue: Catalogue) => {
    // Simulate PDF download
    console.log(`Downloading ${catalogue.name}.pdf`);
    // In a real application, this would trigger a file download
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">
          Gestion des Catalogues
        </h1>
        <button
          onClick={handleAdd}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Télécharger un Catalogue</span>
        </button>
      </div>

      {/* Search Bar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Rechercher des catalogues..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Catalogues Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCatalogues.map((catalogue) => (
          <div
            key={catalogue.id}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                      {catalogue.name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {catalogue.category}
                    </p>
                  </div>
                </div>
                <span
                  className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    catalogue.status === "active"
                      ? "bg-green-100 text-green-800"
                      : catalogue.status === "draft"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  catalogue.status === 'active' ? 'Actif' : catalogue.status ===
                  'draft' ? 'Brouillon' : 'Inactif'
                </span>
              </div>

              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {catalogue.description}
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">Taille du fichier:</span>
                  <span className="text-gray-900">{catalogue.fileSize}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">Téléchargements:</span>
                  <span className="text-gray-900">{catalogue.downloads}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500 flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Téléchargé le:
                  </span>
                  <span className="text-gray-900">{catalogue.uploadDate}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => handleDownload(catalogue)}
                  className="flex-1 bg-green-50 text-green-600 px-3 py-2 rounded-lg hover:bg-green-100 transition-colors flex items-center justify-center space-x-1"
                >
                  <Download className="h-4 w-4" />
                  <span>Télécharger</span>
                </button>
                <button
                  onClick={() => handleEdit(catalogue)}
                  className="bg-blue-50 text-blue-600 px-3 py-2 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <Upload className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(catalogue.id)}
                  className="bg-red-50 text-red-600 px-3 py-2 rounded-lg hover:bg-red-100 transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {showModal && (
        <Modal
          title={editingCatalogue ? "Update Catalogue" : "Upload New Catalogue"}
          onClose={() => setShowModal(false)}
        >
          <form className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Catalogue Name
              </label>
              <input
                type="text"
                defaultValue={editingCatalogue?.name || ""}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                rows={3}
                defaultValue={editingCatalogue?.description || ""}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  defaultValue={editingCatalogue?.category || "General"}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="General">General</option>
                  <option value="Electronics">Electronics</option>
                  <option value="Seasonal">Seasonal</option>
                  <option value="Premium">Premium</option>
                  <option value="Budget">Budget</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  defaultValue={editingCatalogue?.status || "draft"}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="draft">Draft</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
            {!editingCatalogue && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PDF File
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-gray-500">PDF files up to 10MB</p>
                  <input type="file" accept=".pdf" className="hidden" />
                </div>
              </div>
            )}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => setShowModal(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingCatalogue ? "Update" : "Upload"} Catalogue
              </button>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
};

export default CatalogueManagement;
