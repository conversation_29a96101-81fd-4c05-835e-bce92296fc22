import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  EntityStatus,
  Priority,
  FileAttachment,
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS,
  FILE_UPLOAD
} from './common';

// ============================================================================
// CATALOGUE INTERFACES
// ============================================================================

/**
 * Catalogue type enum
 */
export enum CatalogueType {
  PRODUCT = 'product',
  PRICE_LIST = 'price_list',
  PROMOTIONAL = 'promotional',
  SEASONAL = 'seasonal',
  TECHNICAL = 'technical',
  TRAINING = 'training',
  MARKETING = 'marketing',
  CORPORATE = 'corporate'
}

/**
 * Catalogue format enum
 */
export enum CatalogueFormat {
  PDF = 'pdf',
  DIGITAL_FLIPBOOK = 'digital_flipbook',
  INTERACTIVE = 'interactive',
  VIDEO = 'video',
  PRESENTATION = 'presentation',
  WEB_PAGE = 'web_page',
  MOBILE_APP = 'mobile_app'
}

/**
 * Distribution channel enum
 */
export enum DistributionChannel {
  EMAIL = 'email',
  WEBSITE = 'website',
  MOBILE_APP = 'mobile_app',
  PRINT = 'print',
  SOCIAL_MEDIA = 'social_media',
  SALES_TEAM = 'sales_team',
  PARTNER_PORTAL = 'partner_portal',
  QR_CODE = 'qr_code'
}

/**
 * Catalogue version information
 */
export interface CatalogueVersion {
  id: string;
  versionNumber: string;
  releaseDate: Timestamp;
  fileUrl: string;
  fileSize: number;
  changelog: string[];
  isActive: boolean;
  downloadCount: number;
  
  // File details
  fileName: string;
  fileFormat: CatalogueFormat;
  fileHash?: string; // For integrity checking
  
  // Approval workflow
  status: 'draft' | 'review' | 'approved' | 'published' | 'archived';
  createdBy: string;
  reviewedBy?: string;
  approvedBy?: string;
  publishedBy?: string;
  
  // Dates
  reviewDate?: Timestamp;
  approvalDate?: Timestamp;
  publishDate?: Timestamp;
  archiveDate?: Timestamp;
}

/**
 * Catalogue content structure
 */
export interface CatalogueContent {
  // Sections and pages
  sections: {
    id: string;
    title: string;
    description?: string;
    pageStart: number;
    pageEnd: number;
    productIds: string[];
    order: number;
  }[];
  
  // Products included
  products: {
    productId: string;
    productName: string;
    productSku: string;
    pageNumber?: number;
    featured: boolean;
    displayOrder: number;
    customDescription?: string;
    customPrice?: number;
    customImages?: string[];
  }[];
  
  // Media assets
  images: {
    id: string;
    url: string;
    alt: string;
    caption?: string;
    pageNumber?: number;
    type: 'product' | 'lifestyle' | 'banner' | 'logo' | 'background';
  }[];
  
  // Text content
  textBlocks: {
    id: string;
    content: string;
    type: 'introduction' | 'description' | 'terms' | 'contact' | 'footer';
    pageNumber?: number;
    position: string;
  }[];
}

/**
 * Catalogue analytics and tracking
 */
export interface CatalogueAnalytics {
  // Download metrics
  totalDownloads: number;
  uniqueDownloads: number;
  downloadsByChannel: Record<DistributionChannel, number>;
  downloadsByRegion: Record<string, number>;
  downloadsByUserType: Record<string, number>;
  
  // Engagement metrics
  averageViewTime: number; // seconds
  pageViews: Record<number, number>; // page number -> view count
  mostViewedProducts: string[]; // product IDs
  
  // Conversion metrics
  leadsGenerated: number;
  salesGenerated: number;
  revenueGenerated: number;
  conversionRate: number; // percentage
  
  // Time-based metrics
  peakDownloadTime: string; // hour of day
  peakDownloadDay: string; // day of week
  seasonalTrends: Record<string, number>; // month -> download count
  
  // User feedback
  averageRating: number;
  ratingCount: number;
  feedbackComments: {
    id: string;
    userId: string;
    userName: string;
    rating: number;
    comment: string;
    date: Timestamp;
    helpful: number;
  }[];
  
  // Last updated
  lastUpdated: Timestamp;
}

/**
 * Catalogue distribution settings
 */
export interface CatalogueDistribution {
  // Channels
  enabledChannels: DistributionChannel[];
  
  // Access control
  isPublic: boolean;
  requiresLogin: boolean;
  allowedUserTypes: string[];
  allowedRegions: string[];
  restrictedCountries: string[];
  
  // Download settings
  allowDownload: boolean;
  allowPrint: boolean;
  allowShare: boolean;
  watermarkEnabled: boolean;
  passwordProtected: boolean;
  password?: string;
  
  // Expiry and scheduling
  publishDate?: Timestamp;
  expiryDate?: Timestamp;
  autoArchive: boolean;
  
  // Notifications
  notifyOnDownload: boolean;
  notifyOnExpiry: boolean;
  notificationEmails: string[];
}

/**
 * Catalogue SEO and marketing
 */
export interface CatalogueSEO {
  // SEO
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  slug: string;
  canonicalUrl?: string;
  
  // Social media
  socialTitle?: string;
  socialDescription?: string;
  socialImage?: string;
  
  // Marketing
  campaignId?: string;
  campaignName?: string;
  marketingTags: string[];
  utmParameters?: {
    source: string;
    medium: string;
    campaign: string;
    term?: string;
    content?: string;
  };
}

/**
 * Main Catalogue interface
 */
export interface Catalogue extends BaseEntity {
  // Basic Information
  name: string;
  description: string;
  type: CatalogueType;
  format: CatalogueFormat;
  
  // Classification
  category: string;
  subcategory?: string;
  tags: string[];
  
  // Content and Structure
  content: CatalogueContent;
  
  // Versions
  currentVersion: string;
  versions: CatalogueVersion[];
  
  // File Information
  fileUrl: string;
  fileName: string;
  fileSize: string; // Human readable (e.g., "2.5 MB")
  fileSizeBytes: number;
  fileFormat: CatalogueFormat;
  pageCount?: number;
  
  // Analytics and Performance
  analytics: CatalogueAnalytics;
  
  // Distribution and Access
  distribution: CatalogueDistribution;
  
  // SEO and Marketing
  seo: CatalogueSEO;
  
  // Relationships
  productIds: string[];
  categoryIds: string[];
  targetAudience: string[];
  relatedCatalogues: string[];
  
  // Workflow and Approval
  status: EntityStatus;
  workflowStatus: 'draft' | 'review' | 'approved' | 'published' | 'archived';
  priority: Priority;
  
  // Dates
  publishDate?: Timestamp;
  expiryDate?: Timestamp;
  lastModifiedDate: Timestamp;
  
  // Team and Ownership
  ownerId: string;
  ownerName: string;
  teamId?: string;
  collaborators: string[];
  
  // Metadata
  notes?: string;
  internalNotes?: string;
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// CATALOGUE FORM INTERFACES
// ============================================================================

/**
 * Catalogue form data interface (for forms)
 */
export interface CatalogueFormData {
  name: string;
  description: string;
  type: CatalogueType;
  format: CatalogueFormat;
  category: string;
  subcategory?: string;
  tags: string[];
  
  // File
  fileUrl: string;
  fileName: string;
  fileSize: string;
  pageCount?: number;
  
  // Distribution
  isPublic: boolean;
  requiresLogin: boolean;
  allowDownload: boolean;
  allowPrint: boolean;
  allowShare: boolean;
  passwordProtected: boolean;
  password?: string;
  
  // Dates
  publishDate?: string; // ISO date string
  expiryDate?: string;
  
  // SEO
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  slug: string;
  
  // Marketing
  campaignId?: string;
  campaignName?: string;
  marketingTags: string[];
  
  // Status
  status: EntityStatus;
  workflowStatus: 'draft' | 'review' | 'approved' | 'published' | 'archived';
  priority: Priority;
  
  // Team
  ownerId: string;
  collaborators: string[];
  
  // Notes
  notes?: string;
  internalNotes?: string;
}

/**
 * Catalogue search/filter interface
 */
export interface CatalogueSearchFilters {
  name?: string;
  description?: string;
  type?: CatalogueType;
  format?: CatalogueFormat;
  category?: string;
  subcategory?: string;
  status?: EntityStatus;
  workflowStatus?: 'draft' | 'review' | 'approved' | 'published' | 'archived';
  priority?: Priority;
  ownerId?: string;
  teamId?: string;
  tags?: string[];
  
  // Date filters
  publishedAfter?: Timestamp;
  publishedBefore?: Timestamp;
  expiresAfter?: Timestamp;
  expiresBefore?: Timestamp;
  createdAfter?: Timestamp;
  createdBefore?: Timestamp;
  
  // Performance filters
  minDownloads?: number;
  maxDownloads?: number;
  minRating?: number;
  
  // Content filters
  productIds?: string[];
  categoryIds?: string[];
  hasExpiry?: boolean;
  isExpired?: boolean;
  isPublic?: boolean;
  requiresLogin?: boolean;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateCatalogueData = CreateEntity<Catalogue>;
export type UpdateCatalogueData = UpdateEntity<Catalogue>;
export type CatalogueWithId = Catalogue & Required<Pick<Catalogue, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate catalogue form data
 */
export const validateCatalogueData = (data: Partial<CatalogueFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Le nom du catalogue est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push({
      field: 'description',
      message: 'La description est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.category || data.category.trim().length === 0) {
    errors.push({
      field: 'category',
      message: 'La catégorie est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.ownerId || data.ownerId.trim().length === 0) {
    errors.push({
      field: 'ownerId',
      message: 'Le propriétaire est requis',
      code: 'REQUIRED'
    });
  }

  // File validation
  if (data.fileUrl && !isValidUrl(data.fileUrl)) {
    errors.push({
      field: 'fileUrl',
      message: 'L\'URL du fichier doit être valide',
      code: 'INVALID_FORMAT'
    });
  }

  // SEO validation
  if (!data.metaTitle || data.metaTitle.trim().length === 0) {
    errors.push({
      field: 'metaTitle',
      message: 'Le titre SEO est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.metaDescription || data.metaDescription.trim().length === 0) {
    errors.push({
      field: 'metaDescription',
      message: 'La description SEO est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.slug || data.slug.trim().length === 0) {
    errors.push({
      field: 'slug',
      message: 'Le slug est requis',
      code: 'REQUIRED'
    });
  }

  // Length validations
  if (data.name && data.name.length > VALIDATION_LIMITS.NAME_MAX_LENGTH) {
    errors.push({
      field: 'name',
      message: `Le nom ne peut pas dépasser ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.description && data.description.length > VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH) {
    errors.push({
      field: 'description',
      message: `La description ne peut pas dépasser ${VALIDATION_LIMITS.DESCRIPTION_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  // Page count validation
  if (data.pageCount !== undefined && data.pageCount < 0) {
    errors.push({
      field: 'pageCount',
      message: 'Le nombre de pages ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  // Password validation
  if (data.passwordProtected && (!data.password || data.password.length < 6)) {
    errors.push({
      field: 'password',
      message: 'Le mot de passe doit contenir au moins 6 caractères',
      code: 'INVALID_LENGTH'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Check if URL is valid
 */
const isValidUrl = (string: string): boolean => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

/**
 * Sanitize catalogue data before saving
 */
export const sanitizeCatalogueData = (data: CatalogueFormData): CatalogueFormData => {
  return {
    ...data,
    name: sanitizeString(data.name),
    description: sanitizeString(data.description),
    category: sanitizeString(data.category),
    subcategory: data.subcategory ? sanitizeString(data.subcategory) : undefined,
    fileName: data.fileName ? sanitizeString(data.fileName) : '',
    metaTitle: sanitizeString(data.metaTitle),
    metaDescription: sanitizeString(data.metaDescription),
    slug: generateSlug(data.slug || data.name),
    campaignName: data.campaignName ? sanitizeString(data.campaignName) : undefined,
    notes: data.notes ? sanitizeString(data.notes) : undefined,
    internalNotes: data.internalNotes ? sanitizeString(data.internalNotes) : undefined
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate slug from string
 */
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

/**
 * Check if catalogue is expired
 */
export const isCatalogueExpired = (catalogue: Catalogue): boolean => {
  if (!catalogue.expiryDate) return false;
  return catalogue.expiryDate.toDate() < new Date();
};

/**
 * Check if catalogue is published
 */
export const isCataloguePublished = (catalogue: Catalogue): boolean => {
  return catalogue.workflowStatus === 'published' && 
         catalogue.status === EntityStatus.ACTIVE;
};

/**
 * Get catalogue file extension
 */
export const getCatalogueFileExtension = (catalogue: Catalogue): string => {
  const url = catalogue.fileUrl;
  const extension = url.split('.').pop()?.toLowerCase();
  return extension || '';
};

/**
 * Calculate catalogue engagement score
 */
export const calculateEngagementScore = (catalogue: Catalogue): number => {
  const { analytics } = catalogue;
  const downloadScore = Math.min(analytics.totalDownloads / 100, 1) * 30;
  const ratingScore = (analytics.averageRating / 5) * 30;
  const conversionScore = analytics.conversionRate * 40;
  
  return downloadScore + ratingScore + conversionScore;
};

/**
 * Get catalogue performance rating
 */
export const getCataloguePerformanceRating = (catalogue: Catalogue): 'low' | 'medium' | 'high' => {
  const score = calculateEngagementScore(catalogue);
  
  if (score >= 70) return 'high';
  if (score >= 40) return 'medium';
  return 'low';
};

/**
 * Format file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Check if user can download catalogue
 */
export const canDownloadCatalogue = (catalogue: Catalogue, userType?: string, userRegion?: string): boolean => {
  const { distribution } = catalogue;
  
  if (!distribution.allowDownload) return false;
  if (!isCataloguePublished(catalogue)) return false;
  if (isCatalogueExpired(catalogue)) return false;
  
  if (!distribution.isPublic && distribution.requiresLogin) {
    if (userType && distribution.allowedUserTypes.length > 0) {
      if (!distribution.allowedUserTypes.includes(userType)) return false;
    }
    
    if (userRegion && distribution.allowedRegions.length > 0) {
      if (!distribution.allowedRegions.includes(userRegion)) return false;
    }
    
    if (userRegion && distribution.restrictedCountries.includes(userRegion)) {
      return false;
    }
  }
  
  return true;
};
