# TypeScript Models Documentation

This directory contains comprehensive TypeScript models, interfaces, types, and validation schemas for all entities in the VitaBrosse commercial management application.

## 📁 File Structure

```
src/models/
├── common.ts           # Common types, enums, and utilities
├── Client.ts           # Client entity model
├── Commercial.ts       # Commercial entity model  
├── Product.ts          # Product entity model
├── Order.ts            # Order entity model
├── Merchandizer.ts     # Merchandizer entity model
├── Catalogue.ts        # Catalogue entity model
├── index.ts            # Export all models
└── README.md           # This documentation
```

## 🎯 Features

### **Comprehensive Type Safety**
- Full TypeScript interfaces for all entities
- Strict typing for all properties and methods
- Generic types for CRUD operations
- Utility types for form handling

### **Validation & Sanitization**
- Built-in validation functions for all entities
- Data sanitization before saving
- Comprehensive error handling
- Business logic validation

### **Utility Functions**
- Helper functions for calculations
- Display formatting utilities
- Status checking functions
- Performance metrics calculations

### **Enums & Constants**
- Predefined enums for all status types
- Validation limits and constraints
- File upload specifications
- Default pagination settings

## 📋 Available Models

### **1. Client Model** (`Client.ts`)
**Purpose**: Manage client/customer information

**Key Features**:
- Personal and company information
- Contact details and addresses
- Financial information and credit limits
- Preferences and communication settings
- Performance statistics and analytics
- Relationship management

**Main Interface**: `Client`
**Form Interface**: `ClientFormData`
**Search Interface**: `ClientSearchFilters`

### **2. Commercial Model** (`Commercial.ts`)
**Purpose**: Manage sales representatives and their performance

**Key Features**:
- Employee information and hierarchy
- Territory and client assignments
- Performance metrics and targets
- Compensation and commission tracking
- Skills and certifications
- Schedule and availability

**Main Interface**: `Commercial`
**Form Interface**: `CommercialFormData`
**Search Interface**: `CommercialSearchFilters`

### **3. Product Model** (`Product.ts`)
**Purpose**: Comprehensive product catalog management

**Key Features**:
- Product information and specifications
- Pricing and inventory management
- Media assets and documentation
- SEO and marketing optimization
- Analytics and performance tracking
- Variants and options

**Main Interface**: `Product`
**Form Interface**: `ProductFormData`
**Search Interface**: `ProductSearchFilters`

### **4. Order Model** (`Order.ts`)
**Purpose**: Complete order management system

**Key Features**:
- Multi-line item orders
- Customer and commercial assignment
- Shipping and billing addresses
- Payment processing and tracking
- Fulfillment workflow
- Discounts and tax calculations

**Main Interface**: `Order`
**Form Interface**: `OrderFormData`
**Search Interface**: `OrderSearchFilters`

### **5. Merchandizer Model** (`Merchandizer.ts`)
**Purpose**: Field merchandiser and store visit management

**Key Features**:
- Store assignments and territories
- Visit scheduling and reporting
- Performance metrics and KPIs
- Equipment and resource tracking
- Compensation and targets
- Analytics and insights

**Main Interface**: `Merchandizer`
**Form Interface**: `MerchandizerFormData`
**Search Interface**: `MerchandizerSearchFilters`

### **6. Catalogue Model** (`Catalogue.ts`)
**Purpose**: Digital catalog and document management

**Key Features**:
- Multi-format catalog support
- Version control and approval workflow
- Distribution and access control
- Analytics and engagement tracking
- SEO and marketing optimization
- Content management

**Main Interface**: `Catalogue`
**Form Interface**: `CatalogueFormData`
**Search Interface**: `CatalogueSearchFilters`

## 🔧 Usage Examples

### **Basic Import**
```typescript
import { Client, validateClientData, sanitizeClientData } from '../models';
```

### **Creating a New Entity**
```typescript
import { CreateClientData, Client } from '../models';

const newClientData: CreateClientData = {
  name: 'John Doe',
  email: '<EMAIL>',
  company: 'Acme Corp',
  // ... other required fields
};
```

### **Form Validation**
```typescript
import { validateClientData, ClientFormData } from '../models';

const formData: Partial<ClientFormData> = {
  name: 'John Doe',
  email: 'invalid-email'
};

const validation = validateClientData(formData);
if (!validation.isValid) {
  console.log('Validation errors:', validation.errors);
}
```

### **Data Sanitization**
```typescript
import { sanitizeClientData, ClientFormData } from '../models';

const rawData: ClientFormData = {
  name: '  John Doe  ',
  email: '<EMAIL>',
  // ... other fields
};

const cleanData = sanitizeClientData(rawData);
// Result: { name: 'John Doe', email: '<EMAIL>', ... }
```

### **Utility Functions**
```typescript
import { 
  calculateProfitMargin, 
  isLowStock, 
  getOrderStatusText,
  formatFileSize 
} from '../models';

// Product utilities
const margin = calculateProfitMargin(product);
const needsRestock = isLowStock(product);

// Order utilities
const statusText = getOrderStatusText(order.status);

// File utilities
const sizeText = formatFileSize(1024000); // "1 MB"
```

## 🔍 Common Patterns

### **CRUD Type Aliases**
Every model provides these type aliases:
```typescript
type CreateEntityData = CreateEntity<EntityType>;
type UpdateEntityData = UpdateEntity<EntityType>;
type EntityWithId = EntityType & Required<Pick<EntityType, 'id'>>;
```

### **Validation Pattern**
All models follow this validation pattern:
```typescript
interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

interface ValidationError {
  field: string;
  message: string;
  code: string;
}
```

### **Search/Filter Pattern**
All models provide comprehensive search interfaces:
```typescript
interface EntitySearchFilters {
  // Basic fields
  name?: string;
  status?: EntityStatus;
  
  // Date ranges
  createdAfter?: Timestamp;
  createdBefore?: Timestamp;
  
  // Custom filters per entity
  // ...
}
```

## 📊 Enums and Constants

### **Common Enums**
```typescript
enum EntityStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived'
}

enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}
```

### **Validation Constants**
```typescript
const VALIDATION_LIMITS = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  EMAIL_MAX_LENGTH: 255,
  PHONE_MIN_LENGTH: 10,
  PHONE_MAX_LENGTH: 20
} as const;
```

## 🛡️ Type Safety Features

### **Strict Typing**
- All properties are strictly typed
- No `any` types used
- Comprehensive union types for status fields
- Optional vs required field distinction

### **Generic Utilities**
```typescript
// Make all properties optional except specified ones
type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

// Create type for entity creation (without id, timestamps)
type CreateEntity<T extends BaseEntity> = Omit<T, 'id' | 'createdAt' | 'updatedAt'>;
```

### **Form Handling**
- Separate interfaces for form data vs entity data
- Date string handling for form inputs
- Simplified nested objects for forms

## 🔄 Integration with Firebase

All models are designed to work seamlessly with Firebase:
- Timestamp types for date fields
- Firestore document structure compatibility
- Automatic ID generation support
- Audit trail integration

## 📈 Performance Considerations

- Lazy loading of complex nested objects
- Efficient search and filter interfaces
- Optimized for large datasets
- Memory-efficient type definitions

## 🧪 Testing Support

Models include utilities for testing:
- Mock data generation helpers
- Validation test cases
- Type assertion utilities
- Test data factories

## 📝 Best Practices

1. **Always validate data** before saving
2. **Sanitize user input** using provided functions
3. **Use type aliases** for CRUD operations
4. **Leverage utility functions** for calculations
5. **Follow naming conventions** consistently
6. **Use enums** instead of string literals
7. **Handle optional fields** properly
8. **Implement proper error handling**

## 🔮 Future Enhancements

- GraphQL schema generation
- OpenAPI specification export
- Automatic mock data generation
- Real-time validation
- Advanced search capabilities
- Multi-language support
