import React, { useState } from "react";
import { initializeFirebaseData } from "../utils/initializeData";
import { Database, RefreshCw } from "lucide-react";

const DataInitializer: React.FC = () => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const handleInitialize = async () => {
    setIsInitializing(true);
    setMessage(null);

    try {
      const success = await initializeFirebaseData();
      if (success) {
        setMessage("✅ Firebase data initialized successfully!");
      } else {
        setMessage("❌ Failed to initialize Firebase data");
      }
    } catch (error) {
      setMessage("❌ Error: " + (error as Error).message);
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div className="flex items-center space-x-3 mb-4">
        <Database className="h-5 w-5 text-blue-600" />
        <h2 className="text-lg font-semibold text-gray-900">
          Firebase Data Initializer
        </h2>
      </div>

      <p className="text-sm text-gray-600 mb-4">
        Initialize Firebase with sample data including clients, commercials,
        products, and orders.
      </p>

      <button
        onClick={handleInitialize}
        disabled={isInitializing}
        className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
          isInitializing
            ? "bg-gray-100 text-gray-400 cursor-not-allowed"
            : "bg-blue-600 text-white hover:bg-blue-700"
        }`}
      >
        {isInitializing ? (
          <>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Initializing...</span>
          </>
        ) : (
          <>
            <Database className="h-4 w-4" />
            <span>Initialize Sample Data</span>
          </>
        )}
      </button>

      {message && (
        <div className="mt-4 p-3 rounded-lg bg-gray-50 border border-gray-200">
          <p className="text-sm font-medium">{message}</p>
        </div>
      )}
    </div>
  );
};

export default DataInitializer;
