import React from "react";
import VitaBrosseLogo from "../assets/vitabrosse-logo.svg";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "default" | "logovita";
  showText?: boolean;
}

const Logo: React.FC<LogoProps> = ({
  className = "",
  size = "md",
  variant = "logovita",
  showText = true,
}) => {
  const sizeClasses = {
    sm: showText ? "h-8" : "h-8 w-8",
    md: showText ? "h-12" : "h-12 w-12",
    lg: showText ? "h-16" : "h-16 w-16",
    xl: showText ? "h-20" : "h-20 w-20",
  };

  if (variant === "logovita") {
    return (
      <div
        className={`${sizeClasses[size]} ${className} flex items-center justify-center`}
      >
        <img
          src={VitaBrosseLogo}
          alt="VitaBrosse Logo"
          className="w-full h-full object-contain"
        />
      </div>
    );
  }

  // Logo par défaut
  return (
    <div className={`${sizeClasses[size]} ${className}`}>
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Background circle */}
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="url(#logoGradient)"
          stroke="#1E40AF"
          strokeWidth="2"
        />

        {/* Logo text "V" */}
        <path
          d="M25 25 L45 70 L55 70 L75 25 L65 25 L50 60 L35 25 Z"
          fill="white"
          stroke="white"
          strokeWidth="1"
        />

        {/* Gradient definition */}
        <defs>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3B82F6" />
            <stop offset="100%" stopColor="#1E40AF" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
};

export default Logo;
