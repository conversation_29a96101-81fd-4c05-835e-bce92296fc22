// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore, initializeFirestore, persistentLocalCache, persistentMultipleTabManager, enableNetwork, Firestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDst1GCzGuG0u_aLmcU53jv7ePyr_6vt-A",
  authDomain: "commercialapp-12451.firebaseapp.com",
  projectId: "commercialapp-12451",
  storageBucket: "commercialapp-12451.firebasestorage.app",
  messagingSenderId: "87916737597",
  appId: "1:87916737597:web:564d9919ca31e0c386e654"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);

// Initialize Firestore with custom settings to avoid QUIC issues
let db: Firestore;
try {
  // Try to initialize with persistence settings
  db = initializeFirestore(app, {
    localCache: persistentLocalCache({
      tabManager: persistentMultipleTabManager()
    })
  });
} catch (error) {
  // Fallback to regular initialization if persistence fails
  console.warn('Firestore persistence failed, using default configuration:', error);
  db = getFirestore(app);
}

export { db };
export const storage = getStorage(app);

// Ensure network is enabled with retry logic
const enableFirestoreNetwork = async (retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      await enableNetwork(db);
      console.log('Firestore network enabled successfully');
      break;
    } catch (error) {
      console.warn(`Firestore network enable attempt ${i + 1} failed:`, error);
      if (i === retries - 1) {
        console.error('Failed to enable Firestore network after all retries');
      } else {
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }
};

// Initialize network with retry logic
enableFirestoreNetwork();

export default app;
