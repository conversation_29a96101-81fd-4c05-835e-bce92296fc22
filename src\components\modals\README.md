# CRUD Modals Documentation

This directory contains comprehensive CRUD (Create, Read, Update, Delete) modals for all entities in the VitaBrosse commercial management application.

## Available Modals

### 1. ClientModal
**File**: `ClientModal.tsx`
**Purpose**: Manage client information including personal details, company, and contact information.

**Fields**:
- Name (required)
- Email (required)
- Phone (required)
- Company (required)
- Address
- Status (active/inactive)

### 2. CommercialModal
**File**: `CommercialModal.tsx`
**Purpose**: Manage commercial representatives and their territories.

**Fields**:
- Name (required)
- Email (required)
- Phone (required)
- Territory (required)
- Status (active/inactive)

### 3. ProductModal
**File**: `ProductModal.tsx`
**Purpose**: Manage product catalog with pricing and inventory.

**Fields**:
- Product Name (required)
- Description (required)
- Price (required)
- Stock quantity
- Category (required)
- Image URL
- Image preview

### 4. OrderModal
**File**: `OrderModal.tsx`
**Purpose**: Manage customer orders with multiple products and calculations.

**Features**:
- Order number generation
- Client selection
- Commercial assignment
- Multiple product lines
- Automatic total calculation
- Order status management
- Date management

**Fields**:
- Order Number (required)
- Client (required)
- Commercial (required)
- Products (required, multiple)
- Status (pending/confirmed/delivered/cancelled)
- Order Date (required)
- Delivery Date

### 5. MerchandizerModal
**File**: `MerchandizerModal.tsx`
**Purpose**: Manage merchandisers and their performance metrics.

**Fields**:
- Name (required)
- Email (required)
- Phone (required)
- Territory (required)
- Number of stores
- Performance percentage (0-100)
- Last visit date
- Status (active/inactive)

### 6. CatalogueModal
**File**: `CatalogueModal.tsx`
**Purpose**: Manage product catalogues and documentation.

**Features**:
- File upload support
- File preview
- Download functionality
- Category management

**Fields**:
- Catalogue Name (required)
- Description (required)
- Category (required)
- File URL
- File size
- Download count (read-only)
- Status (draft/active/inactive)

## Usage

### Basic Usage

```tsx
import { ClientModal } from '../components/modals';

const MyComponent = () => {
  const [modalState, setModalState] = useState({
    isOpen: false,
    mode: 'create' as 'create' | 'edit' | 'view',
    client: null
  });

  return (
    <>
      <button onClick={() => setModalState({
        isOpen: true, 
        mode: 'create', 
        client: null
      })}>
        New Client
      </button>
      
      <ClientModal
        isOpen={modalState.isOpen}
        onClose={() => setModalState(prev => ({ ...prev, isOpen: false }))}
        client={modalState.client}
        mode={modalState.mode}
      />
    </>
  );
};
```

### Modal Modes

Each modal supports three modes:

1. **Create Mode** (`mode="create"`)
   - Empty form for new entries
   - Save button creates new record
   - All fields editable

2. **Edit Mode** (`mode="edit"`)
   - Pre-filled form with existing data
   - Save button updates existing record
   - All fields editable

3. **View Mode** (`mode="view"`)
   - Read-only display of data
   - No save button
   - All fields disabled

### Props Interface

All modals follow the same prop interface pattern:

```tsx
interface ModalProps {
  isOpen: boolean;           // Controls modal visibility
  onClose: () => void;       // Callback when modal closes
  [entity]?: Entity | null;  // Entity data for edit/view modes
  mode: 'create' | 'edit' | 'view'; // Modal operation mode
}
```

## Features

### Form Validation
- Required field validation
- Email format validation
- Number range validation
- URL format validation
- Custom business logic validation

### Error Handling
- Network error handling
- Validation error display
- User-friendly error messages
- Loading states during operations

### User Experience
- Responsive design
- Keyboard navigation
- Loading indicators
- Success feedback
- Confirmation dialogs

### Firebase Integration
- Automatic CRUD operations
- Real-time data updates
- Error handling
- Optimistic updates

## Styling

All modals use Tailwind CSS for consistent styling:
- Modal overlay with backdrop
- Responsive grid layouts
- Form field styling
- Button states and hover effects
- Status badges and indicators

## Dependencies

- React 18+
- Lucide React (icons)
- Firebase (backend)
- Tailwind CSS (styling)
- Custom hooks (useApp)

## File Structure

```
src/components/modals/
├── ClientModal.tsx
├── CommercialModal.tsx
├── ProductModal.tsx
├── OrderModal.tsx
├── MerchandizerModal.tsx
├── CatalogueModal.tsx
├── index.ts              # Export all modals
└── README.md            # This documentation
```

## Integration with AppContext

All modals integrate with the AppContext for:
- Data fetching
- CRUD operations
- Loading states
- Error handling

Required context methods:
- `add[Entity]()` - Create operations
- `update[Entity]()` - Update operations
- `delete[Entity]()` - Delete operations
- Entity arrays for data display
- Loading states for UI feedback

## Best Practices

1. **Always handle loading states** - Show spinners during operations
2. **Validate user input** - Both client-side and server-side
3. **Provide clear feedback** - Success/error messages
4. **Use consistent patterns** - Follow the established modal structure
5. **Handle edge cases** - Network errors, validation failures
6. **Maintain accessibility** - Keyboard navigation, screen readers
7. **Keep modals focused** - One responsibility per modal
8. **Use proper TypeScript** - Strong typing for all props and state
