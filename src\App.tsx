import React, { useState } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import Sidebar from "./components/Sidebar";
import Dashboard from "./pages/Dashboard";
import CommercialManagement from "./pages/CommercialManagement";
import ClientManagement from "./pages/ClientManagement";
import ProductManagement from "./pages/ProductManagement";
import MerchandizerManagement from "./pages/MerchandizerManagement";
import CatalogueManagement from "./pages/CatalogueManagement";
import OrderManagement from "./pages/OrderManagement";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import ProtectedRoute from "./components/ProtectedRoute";
import { AppProvider } from "./contexts/AppContext";
import { AuthProvider } from "./contexts/AuthContext";

type ActiveSection =
  | "dashboard"
  | "commercials"
  | "clients"
  | "products"
  | "merchandizers"
  | "catalogues"
  | "orders";

// Main App Component
const MainApp: React.FC = () => {
  const [activeSection, setActiveSection] =
    useState<ActiveSection>("dashboard");

  const renderActiveSection = () => {
    switch (activeSection) {
      case "dashboard":
        return <Dashboard />;
      case "commercials":
        return <CommercialManagement />;
      case "clients":
        return <ClientManagement />;
      case "products":
        return <ProductManagement />;
      case "merchandizers":
        return <MerchandizerManagement />;
      case "catalogues":
        return <CatalogueManagement />;
      case "orders":
        return <OrderManagement />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar
        activeSection={activeSection}
        setActiveSection={setActiveSection}
      />
      <main className="flex-1 p-6">{renderActiveSection()}</main>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <MainApp />
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </AppProvider>
    </AuthProvider>
  );
}

export default App;
