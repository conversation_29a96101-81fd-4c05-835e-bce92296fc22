import { FirebaseService } from '../firebase/services';
import { Timestamp } from 'firebase/firestore';

// Sample data for testing
export const sampleClients = [
  {
    name: 'TechCorp Inc.',
    email: '<EMAIL>',
    phone: '******-0123',
    company: 'TechCorp Inc.',
    address: '123 Tech Street, Silicon Valley, CA 94000',
    status: 'active' as const
  },
  {
    name: 'Global Enterprises',
    email: '<EMAIL>',
    phone: '******-0124',
    company: 'Global Enterprises',
    address: '456 Business Ave, New York, NY 10001',
    status: 'active' as const
  },
  {
    name: 'Digital Solutions',
    email: '<EMAIL>',
    phone: '******-0125',
    company: 'Digital Solutions',
    address: '789 Innovation Blvd, Austin, TX 78701',
    status: 'active' as const
  },
  {
    name: 'Innovation Labs',
    email: '<EMAIL>',
    phone: '******-0126',
    company: 'Innovation Labs',
    address: '321 Research Dr, Boston, MA 02101',
    status: 'active' as const
  },
  {
    name: 'StartupTech',
    email: '<EMAIL>',
    phone: '******-0127',
    company: 'StartupTech',
    address: '654 Startup Lane, Seattle, WA 98101',
    status: 'active' as const
  }
];

export const sampleCommercials = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0201',
    territory: 'North America',
    status: 'active' as const
  },
  {
    name: 'Sarah <PERSON>',
    email: '<EMAIL>',
    phone: '******-0202',
    territory: 'Europe',
    status: 'active' as const
  },
  {
    name: 'Mike Brown',
    email: '<EMAIL>',
    phone: '******-0203',
    territory: 'Asia Pacific',
    status: 'active' as const
  }
];

export const sampleProducts = [
  {
    name: 'Laptop Pro 15"',
    description: 'High-performance laptop for professionals',
    price: 1299.99,
    category: 'Electronics',
    stock: 50
  },
  {
    name: 'Wireless Headphones',
    description: 'Premium noise-cancelling headphones',
    price: 299.99,
    category: 'Electronics',
    stock: 100
  },
  {
    name: 'Smartphone X',
    description: 'Latest generation smartphone',
    price: 899.99,
    category: 'Electronics',
    stock: 75
  },
  {
    name: 'Tablet Air',
    description: 'Lightweight tablet for productivity',
    price: 499.99,
    category: 'Electronics',
    stock: 60
  },
  {
    name: 'Smart Watch',
    description: 'Fitness and health tracking watch',
    price: 199.99,
    category: 'Electronics',
    stock: 120
  }
];

export const initializeFirebaseData = async () => {
  try {
    console.log('Initializing Firebase with sample data...');
    
    // Add sample clients
    const clientIds = [];
    for (const client of sampleClients) {
      const clientId = await FirebaseService.addClient(client);
      clientIds.push(clientId);
    }
    
    // Add sample commercials
    const commercialIds = [];
    for (const commercial of sampleCommercials) {
      const commercialId = await FirebaseService.addCommercial(commercial);
      commercialIds.push(commercialId);
    }
    
    // Add sample products
    const productIds = [];
    for (const product of sampleProducts) {
      const productId = await FirebaseService.addProduct(product);
      productIds.push(productId);
    }
    
    // Add sample orders
    const sampleOrders = [
      {
        orderNumber: 'ORD-2024-001',
        clientId: clientIds[0],
        clientName: sampleClients[0].name,
        commercialId: commercialIds[0],
        products: [
          {
            productId: productIds[0],
            productName: sampleProducts[0].name,
            quantity: 2,
            price: sampleProducts[0].price
          },
          {
            productId: productIds[1],
            productName: sampleProducts[1].name,
            quantity: 1,
            price: sampleProducts[1].price
          }
        ],
        totalAmount: (sampleProducts[0].price * 2) + sampleProducts[1].price,
        status: 'confirmed' as const,
        orderDate: Timestamp.now()
      },
      {
        orderNumber: 'ORD-2024-002',
        clientId: clientIds[1],
        clientName: sampleClients[1].name,
        commercialId: commercialIds[1],
        products: [
          {
            productId: productIds[2],
            productName: sampleProducts[2].name,
            quantity: 3,
            price: sampleProducts[2].price
          }
        ],
        totalAmount: sampleProducts[2].price * 3,
        status: 'pending' as const,
        orderDate: Timestamp.now()
      },
      {
        orderNumber: 'ORD-2024-003',
        clientId: clientIds[2],
        clientName: sampleClients[2].name,
        commercialId: commercialIds[2],
        products: [
          {
            productId: productIds[3],
            productName: sampleProducts[3].name,
            quantity: 1,
            price: sampleProducts[3].price
          },
          {
            productId: productIds[4],
            productName: sampleProducts[4].name,
            quantity: 2,
            price: sampleProducts[4].price
          }
        ],
        totalAmount: sampleProducts[3].price + (sampleProducts[4].price * 2),
        status: 'delivered' as const,
        orderDate: Timestamp.now()
      }
    ];
    
    for (const order of sampleOrders) {
      await FirebaseService.addOrder(order);
    }
    
    console.log('Firebase initialized with sample data successfully!');
    return true;
  } catch (error) {
    console.error('Error initializing Firebase data:', error);
    return false;
  }
};

export default initializeFirebaseData;
