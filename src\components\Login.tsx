import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Eye, EyeOff, Mail, Lock, LogIn, AlertCircle } from "lucide-react";
import { useAuth } from "../hooks/useAuth";
import Logo from "./Logo";
import { testFirebaseConnection, checkNetworkStatus } from "../firebase/test";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      console.log("Tentative de connexion avec email:", email);
      await signIn(email, password);
      console.log("Connexion réussie");
    } catch (err: unknown) {
      console.error("Erreur lors de la connexion:", err);

      // Gestion des erreurs Firebase Auth
      let errorCode = "unknown";
      let errorMessage = "Erreur de connexion inconnue";

      if (err && typeof err === "object" && "code" in err) {
        errorCode = (err as { code: string }).code;
      }

      if (err && typeof err === "object" && "message" in err) {
        errorMessage = (err as { message: string }).message;
      }

      console.error("Code d'erreur:", errorCode);
      console.error("Message d'erreur:", errorMessage);

      setError(getErrorMessage(errorCode));
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    console.log("=== Test de la connexion Firebase ===");
    await testFirebaseConnection();
    checkNetworkStatus();
  };

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case "auth/user-not-found":
        return "Aucun utilisateur trouvé avec cette adresse email.";
      case "auth/wrong-password":
        return "Mot de passe incorrect.";
      case "auth/invalid-email":
        return "Adresse email invalide.";
      case "auth/user-disabled":
        return "Ce compte a été désactivé.";
      case "auth/too-many-requests":
        return "Trop de tentatives. Veuillez réessayer plus tard.";
      case "auth/invalid-credential":
        return "Email ou mot de passe incorrect.";
      case "auth/network-request-failed":
        return "Erreur de connexion réseau. Vérifiez votre connexion internet.";
      case "auth/timeout":
        return "Délai d'attente dépassé. Veuillez réessayer.";
      case "auth/email-already-in-use":
        return "Cette adresse email est déjà utilisée.";
      case "auth/weak-password":
        return "Le mot de passe est trop faible.";
      case "auth/operation-not-allowed":
        return "L'opération n'est pas autorisée.";
      case "auth/invalid-action-code":
        return "Code d'action invalide.";
      case "auth/expired-action-code":
        return "Code d'action expiré.";
      case "auth/missing-email":
        return "Adresse email manquante.";
      case "auth/missing-password":
        return "Mot de passe manquant.";
      case "auth/requires-recent-login":
        return "Cette opération nécessite une connexion récente.";
      default:
        console.error("Erreur d'authentification non gérée:", errorCode);
        return "Erreur de connexion. Veuillez réessayer.";
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="flex justify-center">
            <Logo size="xl" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Connexion à votre compte
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Ou{" "}
            <Link
              to="/signup"
              className="font-medium text-indigo-600 hover:text-indigo-500 transition-colors"
            >
              créer un nouveau compte
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">
                Adresse email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="appearance-none rounded-none relative block w-full px-12 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Adresse email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Mot de passe
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  className="appearance-none rounded-none relative block w-full px-12 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Mot de passe"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
            </div>
          </div>

          {error && (
            <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-900"
              >
                Se souvenir de moi
              </label>
            </div>

            <div className="text-sm">
              <Link
                to="/forgot-password"
                className="font-medium text-indigo-600 hover:text-indigo-500 transition-colors"
              >
                Mot de passe oublié?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white transition-colors ${
                loading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              }`}
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  <LogIn className="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" />
                )}
              </span>
              {loading ? "Connexion..." : "Se connecter"}
            </button>
          </div>

          {/* Bouton de test de connexion pour le debugging */}
          <div className="mt-2">
            <button
              type="button"
              onClick={handleTestConnection}
              className="w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Tester la connexion Firebase
            </button>
          </div>
        </form>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            En vous connectant, vous acceptez nos{" "}
            <Link to="/terms" className="text-indigo-600 hover:text-indigo-500">
              conditions d'utilisation
            </Link>{" "}
            et notre{" "}
            <Link
              to="/privacy"
              className="text-indigo-600 hover:text-indigo-500"
            >
              politique de confidentialité
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
