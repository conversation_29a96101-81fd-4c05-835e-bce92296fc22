import React, { createContext, useState, useEffect, ReactNode } from "react";
import {
  FirebaseService,
  Client,
  Commercial,
  Product,
  Order,
} from "../firebase/services";

interface AppContextType {
  // Data
  clients: Client[];
  commercials: Commercial[];
  products: Product[];
  orders: Order[];

  // Loading states
  loading: boolean;
  clientsLoading: boolean;
  commercialsLoading: boolean;
  productsLoading: boolean;
  ordersLoading: boolean;

  // CRUD operations
  addClient: (
    client: Omit<Client, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateClient: (id: string, client: Partial<Client>) => Promise<void>;
  deleteClient: (id: string) => Promise<void>;

  addCommercial: (
    commercial: Omit<Commercial, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateCommercial: (
    id: string,
    commercial: Partial<Commercial>
  ) => Promise<void>;
  deleteCommercial: (id: string) => Promise<void>;

  addProduct: (
    product: Omit<Product, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateProduct: (id: string, product: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;

  addOrder: (
    order: Omit<Order, "id" | "createdAt" | "updatedAt">
  ) => Promise<string>;
  updateOrder: (id: string, order: Partial<Order>) => Promise<void>;
  deleteOrder: (id: string) => Promise<void>;

  // Refresh data
  refreshAll: () => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export { AppContext };

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [commercials, setCommercials] = useState<Commercial[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);

  const [loading, setLoading] = useState(true);
  const [clientsLoading, setClientsLoading] = useState(false);
  const [commercialsLoading, setCommercialsLoading] = useState(false);
  const [productsLoading, setProductsLoading] = useState(false);
  const [ordersLoading, setOrdersLoading] = useState(false);

  // Load all data on component mount
  useEffect(() => {
    refreshAll();
  }, []);

  const refreshAll = async () => {
    setLoading(true);
    try {
      const [clientsData, commercialsData, productsData, ordersData] =
        await Promise.all([
          FirebaseService.getClients(),
          FirebaseService.getCommercials(),
          FirebaseService.getProducts(),
          FirebaseService.getOrders(),
        ]);

      setClients(clientsData);
      setCommercials(commercialsData);
      setProducts(productsData);
      setOrders(ordersData);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Client operations
  const addClient = async (
    client: Omit<Client, "id" | "createdAt" | "updatedAt">
  ) => {
    setClientsLoading(true);
    try {
      const id = await FirebaseService.addClient(client);
      const updatedClients = await FirebaseService.getClients();
      setClients(updatedClients);
      return id;
    } catch (error) {
      console.error("Error adding client:", error);
      throw error;
    } finally {
      setClientsLoading(false);
    }
  };

  const updateClient = async (id: string, client: Partial<Client>) => {
    setClientsLoading(true);
    try {
      await FirebaseService.updateClient(id, client);
      const updatedClients = await FirebaseService.getClients();
      setClients(updatedClients);
    } catch (error) {
      console.error("Error updating client:", error);
      throw error;
    } finally {
      setClientsLoading(false);
    }
  };

  const deleteClient = async (id: string) => {
    setClientsLoading(true);
    try {
      await FirebaseService.deleteClient(id);
      const updatedClients = await FirebaseService.getClients();
      setClients(updatedClients);
    } catch (error) {
      console.error("Error deleting client:", error);
      throw error;
    } finally {
      setClientsLoading(false);
    }
  };

  // Commercial operations
  const addCommercial = async (
    commercial: Omit<Commercial, "id" | "createdAt" | "updatedAt">
  ) => {
    setCommercialsLoading(true);
    try {
      const id = await FirebaseService.addCommercial(commercial);
      const updatedCommercials = await FirebaseService.getCommercials();
      setCommercials(updatedCommercials);
      return id;
    } catch (error) {
      console.error("Error adding commercial:", error);
      throw error;
    } finally {
      setCommercialsLoading(false);
    }
  };

  const updateCommercial = async (
    id: string,
    commercial: Partial<Commercial>
  ) => {
    setCommercialsLoading(true);
    try {
      await FirebaseService.updateCommercial(id, commercial);
      const updatedCommercials = await FirebaseService.getCommercials();
      setCommercials(updatedCommercials);
    } catch (error) {
      console.error("Error updating commercial:", error);
      throw error;
    } finally {
      setCommercialsLoading(false);
    }
  };

  const deleteCommercial = async (id: string) => {
    setCommercialsLoading(true);
    try {
      await FirebaseService.deleteCommercial(id);
      const updatedCommercials = await FirebaseService.getCommercials();
      setCommercials(updatedCommercials);
    } catch (error) {
      console.error("Error deleting commercial:", error);
      throw error;
    } finally {
      setCommercialsLoading(false);
    }
  };

  // Product operations
  const addProduct = async (
    product: Omit<Product, "id" | "createdAt" | "updatedAt">
  ) => {
    setProductsLoading(true);
    try {
      const id = await FirebaseService.addProduct(product);
      const updatedProducts = await FirebaseService.getProducts();
      setProducts(updatedProducts);
      return id;
    } catch (error) {
      console.error("Error adding product:", error);
      throw error;
    } finally {
      setProductsLoading(false);
    }
  };

  const updateProduct = async (id: string, product: Partial<Product>) => {
    setProductsLoading(true);
    try {
      await FirebaseService.updateProduct(id, product);
      const updatedProducts = await FirebaseService.getProducts();
      setProducts(updatedProducts);
    } catch (error) {
      console.error("Error updating product:", error);
      throw error;
    } finally {
      setProductsLoading(false);
    }
  };

  const deleteProduct = async (id: string) => {
    setProductsLoading(true);
    try {
      await FirebaseService.deleteProduct(id);
      const updatedProducts = await FirebaseService.getProducts();
      setProducts(updatedProducts);
    } catch (error) {
      console.error("Error deleting product:", error);
      throw error;
    } finally {
      setProductsLoading(false);
    }
  };

  // Order operations
  const addOrder = async (
    order: Omit<Order, "id" | "createdAt" | "updatedAt">
  ) => {
    setOrdersLoading(true);
    try {
      const id = await FirebaseService.addOrder(order);
      const updatedOrders = await FirebaseService.getOrders();
      setOrders(updatedOrders);
      return id;
    } catch (error) {
      console.error("Error adding order:", error);
      throw error;
    } finally {
      setOrdersLoading(false);
    }
  };

  const updateOrder = async (id: string, order: Partial<Order>) => {
    setOrdersLoading(true);
    try {
      await FirebaseService.updateOrder(id, order);
      const updatedOrders = await FirebaseService.getOrders();
      setOrders(updatedOrders);
    } catch (error) {
      console.error("Error updating order:", error);
      throw error;
    } finally {
      setOrdersLoading(false);
    }
  };

  const deleteOrder = async (id: string) => {
    setOrdersLoading(true);
    try {
      await FirebaseService.deleteOrder(id);
      const updatedOrders = await FirebaseService.getOrders();
      setOrders(updatedOrders);
    } catch (error) {
      console.error("Error deleting order:", error);
      throw error;
    } finally {
      setOrdersLoading(false);
    }
  };

  const value: AppContextType = {
    clients,
    commercials,
    products,
    orders,
    loading,
    clientsLoading,
    commercialsLoading,
    productsLoading,
    ordersLoading,
    addClient,
    updateClient,
    deleteClient,
    addCommercial,
    updateCommercial,
    deleteCommercial,
    addProduct,
    updateProduct,
    deleteProduct,
    addOrder,
    updateOrder,
    deleteOrder,
    refreshAll,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export default AppProvider;
