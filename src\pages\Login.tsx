import React, { useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { Eye, EyeOff, Mail, Lock, AlertCircle } from "lucide-react";

import { useAuth } from "../hooks/useAuth";
import Logo from "../components/ui/Logo";

const Login: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [rememberMe, setRememberMe] = useState(false);

  const navigate = useNavigate();
  const { signIn } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      console.log("Tentative de connexion avec email:", email);
      await signIn(email, password);
      console.log("Connexion réussie, redirection vers /");
      navigate("/");
    } catch (error: unknown) {
      console.error("Erreur lors de la connexion:", error);

      // Gestion des erreurs Firebase Auth
      let errorCode = "unknown";
      if (error && typeof error === "object" && "code" in error) {
        errorCode = (error as { code: string }).code;
      }

      setError(getErrorMessage(errorCode));
    } finally {
      setLoading(false);
    }
  };

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case "auth/user-not-found":
        return "Aucun utilisateur trouvé avec cette adresse email.";
      case "auth/wrong-password":
        return "Mot de passe incorrect.";
      case "auth/invalid-email":
        return "Adresse email invalide.";
      case "auth/user-disabled":
        return "Ce compte a été désactivé.";
      case "auth/too-many-requests":
        return "Trop de tentatives. Veuillez réessayer plus tard.";
      case "auth/invalid-credential":
        return "Email ou mot de passe incorrect.";
      case "auth/network-request-failed":
        return "Erreur de connexion réseau. Vérifiez votre connexion internet.";
      case "auth/timeout":
        return "Délai d'attente dépassé. Veuillez réessayer.";
      case "auth/email-already-in-use":
        return "Cette adresse email est déjà utilisée.";
      case "auth/weak-password":
        return "Le mot de passe est trop faible.";
      case "auth/operation-not-allowed":
        return "L'opération n'est pas autorisée.";
      case "auth/invalid-action-code":
        return "Code d'action invalide.";
      case "auth/expired-action-code":
        return "Code d'action expiré.";
      case "auth/missing-email":
        return "Adresse email manquante.";
      case "auth/missing-password":
        return "Mot de passe manquant.";
      case "auth/requires-recent-login":
        return "Cette opération nécessite une connexion récente.";
      default:
        console.error("Erreur d'authentification non gérée:", errorCode);
        return "Erreur de connexion. Veuillez réessayer.";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>

      <div className="relative z-10 max-w-md w-full">
        {/* Glass morphism card */}
        <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-8">
          <div className="text-center mb-8">
            <Logo size="xl" className="mx-auto mb-6" variant="auth" />
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Bienvenue</h1>
            <p className="text-gray-600">
              Connectez-vous à votre espace VitaBrosse
            </p>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50/80 border border-red-200 rounded-xl flex items-center gap-3 text-red-700">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-semibold text-gray-700 mb-2"
              >
                Adresse email
              </label>
              <div className="relative">
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80 backdrop-blur-sm transition-all duration-300"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-semibold text-gray-700 mb-2"
              >
                Mot de passe
              </label>
              <div className="relative">
                <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full pl-12 pr-14 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80 backdrop-blur-sm transition-all duration-300"
                  placeholder="Votre mot de passe"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="remember"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label
                  htmlFor="remember"
                  className="ml-2 text-sm text-gray-600"
                >
                  Se souvenir de moi
                </label>
              </div>
              <Link
                to="/forgot-password"
                className="text-sm text-blue-600 hover:text-blue-500 font-medium"
              >
                Mot de passe oublié ?
              </Link>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-xl hover:from-blue-700 hover:to-indigo-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-semibold transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]"
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Connexion...
                </div>
              ) : (
                "Se connecter"
              )}
            </button>
          </form>

          <div className="mt-8 text-center">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">ou</span>
              </div>
            </div>

            <p className="mt-6 text-gray-600">
              Pas encore de compte ?{" "}
              <Link
                to="/signup"
                className="text-blue-600 hover:text-blue-500 font-semibold"
              >
                Créer un compte
              </Link>
            </p>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute -top-4 -right-4 w-20 h-20 bg-blue-500/20 rounded-full blur-xl"></div>
        <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-indigo-500/20 rounded-full blur-xl"></div>
      </div>
    </div>
  );
};

export default Login;
