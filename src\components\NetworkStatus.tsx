import React, { useState, useEffect } from "react";
import { Wifi, WifiOff, AlertCircle } from "lucide-react";

interface NetworkStatusProps {
  show: boolean;
  onRetry?: () => void;
}

const NetworkStatus: React.FC<NetworkStatusProps> = ({ show, onRetry }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showRetryButton, setShowRetryButton] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  useEffect(() => {
    if (show && !isOnline) {
      const timer = setTimeout(() => {
        setShowRetryButton(true);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [show, isOnline]);

  if (!show) return null;

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {isOnline ? (
              <AlertCircle className="h-5 w-5 text-orange-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900">
              {isOnline ? "Problème de connexion" : "Connexion internet perdue"}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {isOnline
                ? "Problème de connexion avec le serveur. Tentative de reconnexion..."
                : "Vérifiez votre connexion internet et réessayez."}
            </p>
          </div>
        </div>

        {showRetryButton && onRetry && (
          <div className="mt-3 flex justify-end">
            <button
              onClick={onRetry}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-1"
            >
              <Wifi className="h-4 w-4" />
              <span>Réessayer</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NetworkStatus;
