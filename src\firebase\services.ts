import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  limit,
  onSnapshot,
  Timestamp,
  Query,
  DocumentData
} from "firebase/firestore";
import { db } from "./config";

// Types for our data structures
export interface Client {
  id?: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  address: string;
  status: 'active' | 'inactive';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Commercial {
  id?: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone: string;
  mobile?: string;
  employeeId: string;
  role: string;
  department: string;
  territory: string;
  status: 'active' | 'inactive';

  // Account information for mobile app
  account: {
    username: string;
    email: string;
    temporaryPassword?: string;
    accountStatus: 'active' | 'inactive' | 'suspended' | 'pending_activation' | 'locked';
    isFirstLogin: boolean;
    mustChangePassword: boolean;
    permissions: string[];
    canAccessOffline: boolean;
    maxOfflineDays: number;
    requireBiometric: boolean;
    sessionTimeout: number;
    allowMultipleDevices: boolean;
    canViewAllClients: boolean;
    canEditPricing: boolean;
    maxDiscountPercentage: number;
    requireApprovalAbove: number;
    createdBy: string;
    createdDate: Timestamp;
  };

  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Product {
  id?: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  imageUrl?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Order {
  id?: string;
  orderNumber: string;
  clientId: string;
  clientName: string;
  commercialId: string;
  products: Array<{
    productId: string;
    productName: string;
    quantity: number;
    price: number;
  }>;
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'delivered' | 'cancelled';
  orderDate: Timestamp;
  deliveryDate?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Merchandizer {
  id?: string;
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  phone: string;
  mobile?: string;
  employeeId?: string;
  type: string;
  department?: string;
  territory: string;
  stores: number;
  lastVisit: Timestamp;
  performance: number;
  status: 'active' | 'inactive';

  // Account information for mobile app
  account: {
    username: string;
    email: string;
    temporaryPassword?: string;
    accountStatus: 'active' | 'inactive' | 'suspended' | 'pending_activation' | 'locked';
    isFirstLogin: boolean;
    mustChangePassword: boolean;
    permissions: string[];
    canAccessOffline: boolean;
    maxOfflineDays: number;
    requireBiometric: boolean;
    sessionTimeout: number;
    allowMultipleDevices: boolean;
    createdBy: string;
    createdDate: Timestamp;
  };

  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Catalogue {
  id?: string;
  name: string;
  description: string;
  category: string;
  fileUrl?: string;
  fileSize: string;
  downloads: number;
  status: 'active' | 'inactive' | 'draft';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Firebase service class
export class FirebaseService {
  // Generic CRUD operations
  static async addDocument(collectionName: string, data: any) {
    try {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error) {
      console.error("Error adding document: ", error);
      throw error;
    }
  }

  static async getDocuments(collectionName: string, orderByField?: string, limitCount?: number) {
    try {
      let q = query(collection(db, collectionName));
      
      if (orderByField) {
        q = query(q, orderBy(orderByField, 'desc'));
      }
      
      if (limitCount) {
        q = query(q, limit(limitCount));
      }

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error("Error getting documents: ", error);
      throw error;
    }
  }

  static async updateDocument(collectionName: string, docId: string, data: any) {
    try {
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, {
        ...data,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error("Error updating document: ", error);
      throw error;
    }
  }

  static async deleteDocument(collectionName: string, docId: string) {
    try {
      await deleteDoc(doc(db, collectionName, docId));
    } catch (error) {
      console.error("Error deleting document: ", error);
      throw error;
    }
  }

  // Real-time listeners
  static subscribeToCollection(collectionName: string, callback: (data: any[]) => void) {
    const q = query(collection(db, collectionName), orderBy('createdAt', 'desc'));
    
    return onSnapshot(q, (querySnapshot) => {
      const docs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      callback(docs);
    });
  }

  // Specific methods for each entity
  
  // Clients
  static async addClient(client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>) {
    return this.addDocument('clients', client);
  }

  static async getClients(): Promise<Client[]> {
    return this.getDocuments('clients', 'createdAt') as Promise<Client[]>;
  }

  static async updateClient(clientId: string, client: Partial<Client>) {
    return this.updateDocument('clients', clientId, client);
  }

  static async deleteClient(clientId: string) {
    return this.deleteDocument('clients', clientId);
  }

  // Commercials
  static async addCommercial(commercial: Omit<Commercial, 'id' | 'createdAt' | 'updatedAt'>) {
    return this.addDocument('commercials', commercial);
  }

  static async getCommercials(): Promise<Commercial[]> {
    return this.getDocuments('commercials', 'createdAt') as Promise<Commercial[]>;
  }

  static async updateCommercial(commercialId: string, commercial: Partial<Commercial>) {
    return this.updateDocument('commercials', commercialId, commercial);
  }

  static async deleteCommercial(commercialId: string) {
    return this.deleteDocument('commercials', commercialId);
  }

  // Products
  static async addProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) {
    return this.addDocument('products', product);
  }

  static async getProducts(): Promise<Product[]> {
    return this.getDocuments('products', 'createdAt') as Promise<Product[]>;
  }

  static async updateProduct(productId: string, product: Partial<Product>) {
    return this.updateDocument('products', productId, product);
  }

  static async deleteProduct(productId: string) {
    return this.deleteDocument('products', productId);
  }

  // Orders
  static async addOrder(order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) {
    return this.addDocument('orders', order);
  }

  static async getOrders(): Promise<Order[]> {
    return this.getDocuments('orders', 'createdAt') as Promise<Order[]>;
  }

  static async updateOrder(orderId: string, order: Partial<Order>) {
    return this.updateDocument('orders', orderId, order);
  }

  static async deleteOrder(orderId: string) {
    return this.deleteDocument('orders', orderId);
  }

  // Merchandizers
  static async addMerchandizer(merchandizer: Omit<Merchandizer, 'id' | 'createdAt' | 'updatedAt'>) {
    return this.addDocument('merchandizers', merchandizer);
  }

  static async getMerchandizers(): Promise<Merchandizer[]> {
    return this.getDocuments('merchandizers', 'createdAt') as Promise<Merchandizer[]>;
  }

  static async updateMerchandizer(merchandizerId: string, merchandizer: Partial<Merchandizer>) {
    return this.updateDocument('merchandizers', merchandizerId, merchandizer);
  }

  static async deleteMerchandizer(merchandizerId: string) {
    return this.deleteDocument('merchandizers', merchandizerId);
  }

  // Catalogues
  static async addCatalogue(catalogue: Omit<Catalogue, 'id' | 'createdAt' | 'updatedAt'>) {
    return this.addDocument('catalogues', catalogue);
  }

  static async getCatalogues(): Promise<Catalogue[]> {
    return this.getDocuments('catalogues', 'createdAt') as Promise<Catalogue[]>;
  }

  static async updateCatalogue(catalogueId: string, catalogue: Partial<Catalogue>) {
    return this.updateDocument('catalogues', catalogueId, catalogue);
  }

  static async deleteCatalogue(catalogueId: string) {
    return this.deleteDocument('catalogues', catalogueId);
  }

  // Dashboard statistics
  static async getDashboardStats() {
    try {
      const [clients, orders, products, commercials] = await Promise.all([
        this.getClients(),
        this.getOrders(),
        this.getProducts(),
        this.getCommercials()
      ]);

      const activeClients = clients.filter(client => client.status === 'active');
      const totalSales = orders.reduce((sum, order) => sum + order.totalAmount, 0);
      const recentOrders = orders.slice(0, 5);

      // Top clients by order amount
      const clientOrderTotals = new Map();
      orders.forEach(order => {
        const current = clientOrderTotals.get(order.clientId) || { 
          name: order.clientName, 
          amount: 0, 
          orders: 0 
        };
        current.amount += order.totalAmount;
        current.orders += 1;
        clientOrderTotals.set(order.clientId, current);
      });

      const topClients = Array.from(clientOrderTotals.entries())
        .map(([id, data]) => ({ id, ...data }))
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 5);

      return {
        totalSales,
        activeClients: activeClients.length,
        totalProducts: products.length,
        totalOrders: orders.length,
        topClients,
        recentOrders
      };
    } catch (error) {
      console.error("Error getting dashboard stats: ", error);
      throw error;
    }
  }

  // Network error handling and retry logic
  static async withRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
    let lastError: unknown;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: unknown) {
        lastError = error;
        
        // Check if it's a network error
        const errorCode = error instanceof Error && 'code' in error ? (error as { code: string }).code : '';
        const errorMessage = error instanceof Error ? error.message : '';
        
        if (errorCode === 'unavailable' || 
            errorCode === 'deadline-exceeded' || 
            errorMessage.includes('net::ERR_QUIC_PROTOCOL_ERROR')) {
          
          console.warn(`Network error on attempt ${attempt}/${maxRetries}:`, error);
          
          if (attempt < maxRetries) {
            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }
        
        // If it's not a network error or we've exhausted retries, throw immediately
        throw error;
      }
    }
    
    throw lastError;
  }

  // Enhanced methods with retry logic
  static async getDocumentsWithRetry(collectionName: string, orderByField?: string, limitCount?: number) {
    return this.withRetry(() => this.getDocuments(collectionName, orderByField, limitCount));
  }

  static async getDashboardStatsWithRetry() {
    return this.withRetry(() => this.getDashboardStats());
  }
}

export default FirebaseService;
