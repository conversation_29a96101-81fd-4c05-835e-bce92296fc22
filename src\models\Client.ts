import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  EntityStatus, 
  ContactInfo, 
  Address, 
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  isValidEmail,
  isValidPhone,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// CLIENT INTERFACES
// ============================================================================

/**
 * Client type enum
 */
export enum ClientType {
  INDIVIDUAL = 'individual',
  COMPANY = 'company',
  GOVERNMENT = 'government',
  NON_PROFIT = 'non_profit'
}

/**
 * Client category enum
 */
export enum ClientCategory {
  PREMIUM = 'premium',
  STANDARD = 'standard',
  BASIC = 'basic',
  VIP = 'vip'
}

/**
 * Payment terms enum
 */
export enum PaymentTerms {
  NET_15 = 'net_15',
  NET_30 = 'net_30',
  NET_45 = 'net_45',
  NET_60 = 'net_60',
  CASH_ON_DELIVERY = 'cod',
  PREPAID = 'prepaid'
}

/**
 * Client preferences interface
 */
export interface ClientPreferences {
  preferredContactMethod: 'email' | 'phone' | 'sms' | 'mail';
  communicationLanguage: 'fr' | 'en' | 'es' | 'de';
  marketingOptIn: boolean;
  newsletterSubscription: boolean;
  smsNotifications: boolean;
  emailNotifications: boolean;
}

/**
 * Client financial information
 */
export interface ClientFinancialInfo {
  creditLimit: number;
  currentBalance: number;
  paymentTerms: PaymentTerms;
  taxId?: string;
  vatNumber?: string;
  bankAccount?: string;
  paymentMethod: 'credit_card' | 'bank_transfer' | 'check' | 'cash';
}

/**
 * Client statistics
 */
export interface ClientStats {
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderDate?: Timestamp;
  firstOrderDate?: Timestamp;
  loyaltyPoints: number;
  referralCount: number;
}

/**
 * Main Client interface
 */
export interface Client extends BaseEntity {
  // Basic Information
  name: string;
  firstName?: string;
  lastName?: string;
  company: string;
  clientType: ClientType;
  category: ClientCategory;
  
  // Contact Information
  email: string;
  phone: string;
  mobile?: string;
  website?: string;
  
  // Address Information
  address: string;
  billingAddress?: Address;
  shippingAddress?: Address;
  
  // Business Information
  industry?: string;
  companySize?: 'small' | 'medium' | 'large' | 'enterprise';
  annualRevenue?: number;
  
  // Relationship Information
  assignedCommercialId?: string;
  assignedCommercialName?: string;
  accountManagerId?: string;
  
  // Financial Information
  financialInfo: ClientFinancialInfo;
  
  // Preferences
  preferences: ClientPreferences;
  
  // Statistics
  stats: ClientStats;
  
  // Status and Metadata
  status: EntityStatus;
  priority: 'low' | 'medium' | 'high';
  tags: string[];
  notes?: string;
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// CLIENT FORM INTERFACES
// ============================================================================

/**
 * Client form data interface (for forms)
 */
export interface ClientFormData {
  name: string;
  firstName?: string;
  lastName?: string;
  company: string;
  clientType: ClientType;
  category: ClientCategory;
  email: string;
  phone: string;
  mobile?: string;
  website?: string;
  address: string;
  industry?: string;
  companySize?: 'small' | 'medium' | 'large' | 'enterprise';
  status: EntityStatus;
  priority: 'low' | 'medium' | 'high';
  notes?: string;
  tags: string[];
  
  // Financial
  creditLimit: number;
  paymentTerms: PaymentTerms;
  taxId?: string;
  vatNumber?: string;
  
  // Preferences
  preferredContactMethod: 'email' | 'phone' | 'sms' | 'mail';
  communicationLanguage: 'fr' | 'en' | 'es' | 'de';
  marketingOptIn: boolean;
  newsletterSubscription: boolean;
}

/**
 * Client search/filter interface
 */
export interface ClientSearchFilters {
  name?: string;
  company?: string;
  email?: string;
  phone?: string;
  clientType?: ClientType;
  category?: ClientCategory;
  status?: EntityStatus;
  priority?: 'low' | 'medium' | 'high';
  assignedCommercialId?: string;
  industry?: string;
  companySize?: 'small' | 'medium' | 'large' | 'enterprise';
  tags?: string[];
  createdAfter?: Timestamp;
  createdBefore?: Timestamp;
  minTotalSpent?: number;
  maxTotalSpent?: number;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateClientData = CreateEntity<Client>;
export type UpdateClientData = UpdateEntity<Client>;
export type ClientWithId = Client & Required<Pick<Client, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate client form data
 */
export const validateClientData = (data: Partial<ClientFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Le nom est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.company || data.company.trim().length === 0) {
    errors.push({
      field: 'company',
      message: 'L\'entreprise est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.push({
      field: 'email',
      message: 'L\'email est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Format d\'email invalide',
      code: 'INVALID_FORMAT'
    });
  }

  if (!data.phone || data.phone.trim().length === 0) {
    errors.push({
      field: 'phone',
      message: 'Le téléphone est requis',
      code: 'REQUIRED'
    });
  } else if (!isValidPhone(data.phone)) {
    errors.push({
      field: 'phone',
      message: 'Format de téléphone invalide',
      code: 'INVALID_FORMAT'
    });
  }

  // Length validations
  if (data.name && (data.name.length < VALIDATION_LIMITS.NAME_MIN_LENGTH || 
      data.name.length > VALIDATION_LIMITS.NAME_MAX_LENGTH)) {
    errors.push({
      field: 'name',
      message: `Le nom doit contenir entre ${VALIDATION_LIMITS.NAME_MIN_LENGTH} et ${VALIDATION_LIMITS.NAME_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.email && data.email.length > VALIDATION_LIMITS.EMAIL_MAX_LENGTH) {
    errors.push({
      field: 'email',
      message: `L'email ne peut pas dépasser ${VALIDATION_LIMITS.EMAIL_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  if (data.notes && data.notes.length > VALIDATION_LIMITS.NOTES_MAX_LENGTH) {
    errors.push({
      field: 'notes',
      message: `Les notes ne peuvent pas dépasser ${VALIDATION_LIMITS.NOTES_MAX_LENGTH} caractères`,
      code: 'INVALID_LENGTH'
    });
  }

  // Business logic validations
  if (data.creditLimit && data.creditLimit < 0) {
    errors.push({
      field: 'creditLimit',
      message: 'La limite de crédit ne peut pas être négative',
      code: 'INVALID_VALUE'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize client data before saving
 */
export const sanitizeClientData = (data: ClientFormData): ClientFormData => {
  return {
    ...data,
    name: sanitizeString(data.name),
    firstName: data.firstName ? sanitizeString(data.firstName) : undefined,
    lastName: data.lastName ? sanitizeString(data.lastName) : undefined,
    company: sanitizeString(data.company),
    email: data.email.toLowerCase().trim(),
    phone: data.phone.replace(/[\s\-\(\)]/g, ''),
    mobile: data.mobile ? data.mobile.replace(/[\s\-\(\)]/g, '') : undefined,
    website: data.website ? data.website.toLowerCase().trim() : undefined,
    address: sanitizeString(data.address),
    industry: data.industry ? sanitizeString(data.industry) : undefined,
    notes: data.notes ? sanitizeString(data.notes) : undefined,
    taxId: data.taxId ? sanitizeString(data.taxId) : undefined,
    vatNumber: data.vatNumber ? sanitizeString(data.vatNumber) : undefined
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create default client preferences
 */
export const createDefaultClientPreferences = (): ClientPreferences => ({
  preferredContactMethod: 'email',
  communicationLanguage: 'fr',
  marketingOptIn: false,
  newsletterSubscription: false,
  smsNotifications: false,
  emailNotifications: true
});

/**
 * Create default client financial info
 */
export const createDefaultClientFinancialInfo = (): ClientFinancialInfo => ({
  creditLimit: 0,
  currentBalance: 0,
  paymentTerms: PaymentTerms.NET_30,
  paymentMethod: 'credit_card'
});

/**
 * Create default client stats
 */
export const createDefaultClientStats = (): ClientStats => ({
  totalOrders: 0,
  totalSpent: 0,
  averageOrderValue: 0,
  loyaltyPoints: 0,
  referralCount: 0
});

/**
 * Get client display name
 */
export const getClientDisplayName = (client: Client): string => {
  if (client.firstName && client.lastName) {
    return `${client.firstName} ${client.lastName}`;
  }
  return client.name;
};

/**
 * Get client full address
 */
export const getClientFullAddress = (client: Client): string => {
  return client.address || 'Adresse non renseignée';
};

/**
 * Check if client is premium
 */
export const isClientPremium = (client: Client): boolean => {
  return client.category === ClientCategory.PREMIUM || client.category === ClientCategory.VIP;
};

/**
 * Calculate client lifetime value
 */
export const calculateClientLifetimeValue = (client: Client): number => {
  return client.stats.totalSpent;
};

/**
 * Get client risk level based on financial info
 */
export const getClientRiskLevel = (client: Client): 'low' | 'medium' | 'high' => {
  const { creditLimit, currentBalance } = client.financialInfo;
  const utilizationRatio = creditLimit > 0 ? currentBalance / creditLimit : 0;
  
  if (utilizationRatio > 0.8) return 'high';
  if (utilizationRatio > 0.5) return 'medium';
  return 'low';
};
