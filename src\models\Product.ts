import { Timestamp } from 'firebase/firestore';
import { 
  BaseEntity, 
  EntityStatus, 
  FileAttachment,
  AuditTrail, 
  Metadata,
  ValidationResult,
  ValidationError,
  CreateEntity,
  UpdateEntity,
  sanitizeString,
  VALIDATION_LIMITS
} from './common';

// ============================================================================
// PRODUCT INTERFACES
// ============================================================================

/**
 * Product type enum
 */
export enum ProductType {
  PHYSICAL = 'physical',
  DIGITAL = 'digital',
  SERVICE = 'service',
  SUBSCRIPTION = 'subscription',
  BUNDLE = 'bundle'
}

/**
 * Product availability status
 */
export enum ProductAvailability {
  IN_STOCK = 'in_stock',
  OUT_OF_STOCK = 'out_of_stock',
  LOW_STOCK = 'low_stock',
  DISCONTINUED = 'discontinued',
  PRE_ORDER = 'pre_order',
  BACK_ORDER = 'back_order'
}

/**
 * Product condition enum
 */
export enum ProductCondition {
  NEW = 'new',
  REFURBISHED = 'refurbished',
  USED = 'used',
  DAMAGED = 'damaged'
}

/**
 * Product dimensions interface
 */
export interface ProductDimensions {
  length: number; // cm
  width: number;  // cm
  height: number; // cm
  weight: number; // kg
  volume?: number; // cm³
}

/**
 * Product pricing information
 */
export interface ProductPricing {
  basePrice: number;
  salePrice?: number;
  costPrice: number;
  msrp?: number; // Manufacturer's Suggested Retail Price
  currency: string;
  taxRate: number; // percentage
  discountPercentage?: number;
  priceValidFrom: Timestamp;
  priceValidTo?: Timestamp;
  
  // Bulk pricing
  bulkPricing?: {
    minQuantity: number;
    price: number;
  }[];
}

/**
 * Product inventory information
 */
export interface ProductInventory {
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  minimumStock: number;
  maximumStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  
  // Locations
  warehouseLocations: {
    warehouseId: string;
    warehouseName: string;
    quantity: number;
    location: string; // shelf/bin location
  }[];
  
  // Tracking
  lastStockUpdate: Timestamp;
  lastStockCheck: Timestamp;
  stockMovements: {
    date: Timestamp;
    type: 'in' | 'out' | 'adjustment';
    quantity: number;
    reason: string;
    reference?: string;
  }[];
}

/**
 * Product specifications
 */
export interface ProductSpecifications {
  brand?: string;
  model?: string;
  manufacturer?: string;
  manufacturerPartNumber?: string;
  upc?: string; // Universal Product Code
  ean?: string; // European Article Number
  isbn?: string; // International Standard Book Number
  
  // Technical specs
  specifications: {
    name: string;
    value: string;
    unit?: string;
  }[];
  
  // Materials and composition
  materials?: string[];
  composition?: {
    material: string;
    percentage: number;
  }[];
  
  // Certifications
  certifications?: {
    name: string;
    issuer: string;
    number: string;
    validFrom: Timestamp;
    validTo?: Timestamp;
  }[];
}

/**
 * Product media and assets
 */
export interface ProductMedia {
  images: {
    id: string;
    url: string;
    alt: string;
    isPrimary: boolean;
    sortOrder: number;
    size: number;
    dimensions: {
      width: number;
      height: number;
    };
  }[];
  
  videos?: {
    id: string;
    url: string;
    title: string;
    duration: number; // seconds
    thumbnail: string;
    type: 'product_demo' | 'tutorial' | 'review' | 'unboxing';
  }[];
  
  documents?: FileAttachment[];
  
  // 3D models, AR assets, etc.
  assets?: {
    type: '3d_model' | 'ar_asset' | 'cad_file';
    url: string;
    format: string;
    size: number;
  }[];
}

/**
 * Product SEO and marketing
 */
export interface ProductSEO {
  metaTitle?: string;
  metaDescription?: string;
  keywords: string[];
  slug: string;
  canonicalUrl?: string;
  
  // Marketing
  marketingTags: string[];
  promotionalText?: string;
  badges: string[]; // "New", "Sale", "Best Seller", etc.
}

/**
 * Product analytics and performance
 */
export interface ProductAnalytics {
  views: number;
  sales: number;
  revenue: number;
  averageRating: number;
  reviewCount: number;
  returnRate: number;
  conversionRate: number;
  
  // Time-based metrics
  lastSold?: Timestamp;
  firstSold?: Timestamp;
  peakSalesDate?: Timestamp;
  
  // Performance indicators
  isTopSeller: boolean;
  isTrending: boolean;
  seasonalityScore: number;
}

/**
 * Main Product interface
 */
export interface Product extends BaseEntity {
  // Basic Information
  name: string;
  description: string;
  shortDescription?: string;
  sku: string; // Stock Keeping Unit
  barcode?: string;
  
  // Classification
  type: ProductType;
  category: string;
  subcategory?: string;
  tags: string[];
  
  // Pricing and Inventory
  pricing: ProductPricing;
  inventory: ProductInventory;
  
  // Physical Properties
  dimensions?: ProductDimensions;
  condition: ProductCondition;
  
  // Specifications and Details
  specifications: ProductSpecifications;
  
  // Media and Assets
  media: ProductMedia;
  
  // SEO and Marketing
  seo: ProductSEO;
  
  // Analytics
  analytics: ProductAnalytics;
  
  // Relationships
  categoryId?: string;
  supplierId?: string;
  supplierName?: string;
  brandId?: string;
  
  // Variants and Options
  hasVariants: boolean;
  parentProductId?: string; // For product variants
  variantOptions?: {
    name: string; // e.g., "Color", "Size"
    values: string[]; // e.g., ["Red", "Blue"], ["S", "M", "L"]
  }[];
  
  // Availability and Status
  availability: ProductAvailability;
  status: EntityStatus;
  isActive: boolean;
  isFeatured: boolean;
  
  // Dates
  launchDate?: Timestamp;
  discontinueDate?: Timestamp;
  
  // Metadata
  notes?: string;
  internalNotes?: string;
  metadata?: Metadata;
  
  // Audit Information
  audit: AuditTrail;
}

// ============================================================================
// PRODUCT FORM INTERFACES
// ============================================================================

/**
 * Product form data interface (for forms)
 */
export interface ProductFormData {
  name: string;
  description: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  type: ProductType;
  category: string;
  subcategory?: string;
  tags: string[];
  
  // Pricing
  basePrice: number;
  salePrice?: number;
  costPrice: number;
  currency: string;
  taxRate: number;
  
  // Inventory
  currentStock: number;
  minimumStock: number;
  reorderPoint: number;
  reorderQuantity: number;
  
  // Physical
  length?: number;
  width?: number;
  height?: number;
  weight?: number;
  condition: ProductCondition;
  
  // Basic specs
  brand?: string;
  model?: string;
  manufacturer?: string;
  
  // Media
  imageUrl?: string;
  
  // Status
  availability: ProductAvailability;
  status: EntityStatus;
  isActive: boolean;
  isFeatured: boolean;
  
  // SEO
  metaTitle?: string;
  metaDescription?: string;
  keywords: string[];
  
  // Notes
  notes?: string;
  internalNotes?: string;
}

/**
 * Product search/filter interface
 */
export interface ProductSearchFilters {
  name?: string;
  sku?: string;
  barcode?: string;
  category?: string;
  subcategory?: string;
  type?: ProductType;
  brand?: string;
  manufacturer?: string;
  supplierId?: string;
  availability?: ProductAvailability;
  condition?: ProductCondition;
  status?: EntityStatus;
  isActive?: boolean;
  isFeatured?: boolean;
  tags?: string[];
  
  // Price range
  minPrice?: number;
  maxPrice?: number;
  
  // Stock range
  minStock?: number;
  maxStock?: number;
  lowStock?: boolean;
  outOfStock?: boolean;
  
  // Dates
  createdAfter?: Timestamp;
  createdBefore?: Timestamp;
  launchedAfter?: Timestamp;
  launchedBefore?: Timestamp;
  
  // Performance
  minRating?: number;
  minSales?: number;
  isTopSeller?: boolean;
  isTrending?: boolean;
}

// ============================================================================
// TYPE ALIASES
// ============================================================================

export type CreateProductData = CreateEntity<Product>;
export type UpdateProductData = UpdateEntity<Product>;
export type ProductWithId = Product & Required<Pick<Product, 'id'>>;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate product form data
 */
export const validateProductData = (data: Partial<ProductFormData>): ValidationResult => {
  const errors: ValidationError[] = [];

  // Required fields validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push({
      field: 'name',
      message: 'Le nom du produit est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.description || data.description.trim().length === 0) {
    errors.push({
      field: 'description',
      message: 'La description est requise',
      code: 'REQUIRED'
    });
  }

  if (!data.sku || data.sku.trim().length === 0) {
    errors.push({
      field: 'sku',
      message: 'Le SKU est requis',
      code: 'REQUIRED'
    });
  }

  if (!data.category || data.category.trim().length === 0) {
    errors.push({
      field: 'category',
      message: 'La catégorie est requise',
      code: 'REQUIRED'
    });
  }

  // Price validations
  if (data.basePrice !== undefined && data.basePrice <= 0) {
    errors.push({
      field: 'basePrice',
      message: 'Le prix de base doit être supérieur à 0',
      code: 'INVALID_VALUE'
    });
  }

  if (data.costPrice !== undefined && data.costPrice < 0) {
    errors.push({
      field: 'costPrice',
      message: 'Le prix de revient ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.salePrice !== undefined && data.basePrice !== undefined && 
      data.salePrice > data.basePrice) {
    errors.push({
      field: 'salePrice',
      message: 'Le prix de vente ne peut pas être supérieur au prix de base',
      code: 'INVALID_VALUE'
    });
  }

  // Stock validations
  if (data.currentStock !== undefined && data.currentStock < 0) {
    errors.push({
      field: 'currentStock',
      message: 'Le stock actuel ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  if (data.minimumStock !== undefined && data.minimumStock < 0) {
    errors.push({
      field: 'minimumStock',
      message: 'Le stock minimum ne peut pas être négatif',
      code: 'INVALID_VALUE'
    });
  }

  // Dimension validations
  if (data.length !== undefined && data.length <= 0) {
    errors.push({
      field: 'length',
      message: 'La longueur doit être supérieure à 0',
      code: 'INVALID_VALUE'
    });
  }

  if (data.width !== undefined && data.width <= 0) {
    errors.push({
      field: 'width',
      message: 'La largeur doit être supérieure à 0',
      code: 'INVALID_VALUE'
    });
  }

  if (data.height !== undefined && data.height <= 0) {
    errors.push({
      field: 'height',
      message: 'La hauteur doit être supérieure à 0',
      code: 'INVALID_VALUE'
    });
  }

  if (data.weight !== undefined && data.weight <= 0) {
    errors.push({
      field: 'weight',
      message: 'Le poids doit être supérieur à 0',
      code: 'INVALID_VALUE'
    });
  }

  // Tax rate validation
  if (data.taxRate !== undefined && (data.taxRate < 0 || data.taxRate > 100)) {
    errors.push({
      field: 'taxRate',
      message: 'Le taux de taxe doit être entre 0 et 100%',
      code: 'INVALID_VALUE'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Sanitize product data before saving
 */
export const sanitizeProductData = (data: ProductFormData): ProductFormData => {
  return {
    ...data,
    name: sanitizeString(data.name),
    description: sanitizeString(data.description),
    shortDescription: data.shortDescription ? sanitizeString(data.shortDescription) : undefined,
    sku: sanitizeString(data.sku).toUpperCase(),
    barcode: data.barcode ? sanitizeString(data.barcode) : undefined,
    category: sanitizeString(data.category),
    subcategory: data.subcategory ? sanitizeString(data.subcategory) : undefined,
    brand: data.brand ? sanitizeString(data.brand) : undefined,
    model: data.model ? sanitizeString(data.model) : undefined,
    manufacturer: data.manufacturer ? sanitizeString(data.manufacturer) : undefined,
    notes: data.notes ? sanitizeString(data.notes) : undefined,
    internalNotes: data.internalNotes ? sanitizeString(data.internalNotes) : undefined,
    metaTitle: data.metaTitle ? sanitizeString(data.metaTitle) : undefined,
    metaDescription: data.metaDescription ? sanitizeString(data.metaDescription) : undefined
  };
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Calculate product profit margin
 */
export const calculateProfitMargin = (product: Product): number => {
  const { basePrice, costPrice } = product.pricing;
  if (costPrice === 0) return 0;
  return ((basePrice - costPrice) / basePrice) * 100;
};

/**
 * Check if product is low stock
 */
export const isLowStock = (product: Product): boolean => {
  const { availableStock, minimumStock } = product.inventory;
  return availableStock <= minimumStock;
};

/**
 * Check if product is out of stock
 */
export const isOutOfStock = (product: Product): boolean => {
  return product.inventory.availableStock <= 0;
};

/**
 * Get product display price
 */
export const getDisplayPrice = (product: Product): number => {
  return product.pricing.salePrice || product.pricing.basePrice;
};

/**
 * Calculate product volume
 */
export const calculateVolume = (product: Product): number => {
  if (!product.dimensions) return 0;
  const { length, width, height } = product.dimensions;
  return length * width * height;
};

/**
 * Generate product slug for SEO
 */
export const generateProductSlug = (productName: string): string => {
  return productName
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

/**
 * Check if product needs reorder
 */
export const needsReorder = (product: Product): boolean => {
  const { availableStock, reorderPoint } = product.inventory;
  return availableStock <= reorderPoint;
};

/**
 * Get product availability status
 */
export const getAvailabilityStatus = (product: Product): ProductAvailability => {
  if (product.inventory.availableStock <= 0) {
    return ProductAvailability.OUT_OF_STOCK;
  }
  if (isLowStock(product)) {
    return ProductAvailability.LOW_STOCK;
  }
  return ProductAvailability.IN_STOCK;
};
